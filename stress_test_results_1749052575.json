{"summary": {"total_requests": 8802, "successful_requests": 7162, "timeout_requests": 1591, "success_rate": 0.8136787093842308, "timeout_rate": 0.18075437400590774, "avg_response_time": 1.5633551856719792, "median_response_time": 1.8942533731460571, "p95_response_time": 3.0614046216011044, "p99_response_time": 3.223320815563202, "min_response_time": 0.11338973045349121, "max_response_time": 9.697468519210815}, "by_rps": {"5": {"target_rps": 5, "total_requests": 3040, "successful_requests": 3039, "timeout_requests": 0, "success_rate": 0.999671052631579, "timeout_rate": 0.0, "avg_response_time": 0.3485709706903014, "p95_response_time": 0.884371018409729, "actual_throughput": 5.064987167375016}, "10": {"target_rps": 10, "total_requests": 5762, "successful_requests": 4123, "timeout_requests": 1591, "success_rate": 0.7155501561957653, "timeout_rate": 0.27611940298507465, "avg_response_time": 2.4587539800763736, "p95_response_time": 3.125218224525452, "actual_throughput": 6.78500648356737}}, "recommendations": {"max_sustainable_rps": 5, "max_throughput_per_instance": 5.064987167375016, "estimated_instances_for_2000_users": 7, "recommended_safe_rps": 4, "safety_margin": "建议使用80%的最大承载能力作为安全运行点", "queue_size_recommendations": {"max_queue_size": 10, "explanation": "队列大小应能缓冲短期突发流量，建议设置为最大RPS的2倍"}, "monitoring_recommendations": ["监控每秒请求数，确保不超过 4 RPS", "监控超时率，确保低于5%", "监控队列长度，避免积压过多请求", "监控响应时间P95，确保在可接受范围内", "设置自动扩缩容策略，基于队列长度和响应时间", "定期进行吞吐量测试验证容量"], "scaling_strategy": {"scale_up_threshold": "队列长度超过 5 或响应时间P95超过5秒", "scale_down_threshold": "队列长度低于 2 且响应时间P95低于2秒", "min_instances": 2, "max_instances": 10}}, "max_sustainable_rps": 5}