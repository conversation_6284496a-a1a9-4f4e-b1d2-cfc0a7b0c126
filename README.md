# 向量模型服务吞吐测试工具

这是一个专门用于测试向量模型服务性能和吞吐能力的测试套件，可以帮助您：

1. 测试服务的最大承载能力
2. 评估不同负载下的性能表现
3. 模拟真实用户使用场景
4. 估算所需的服务实例数量
5. 提供容量规划建议

## 功能特性

### 🚀 压力测试
- 渐进式负载测试，自动找到最佳并发度
- 详细的性能指标分析（响应时间、吞吐量、成功率）
- 自动生成容量规划建议

### 👥 客户端模拟
- 模拟真实用户行为模式
- 支持多种用户到达模式（均匀、突发、渐进）
- 用户满意度评估

### 📊 性能监控
- 实时监控服务状态
- 队列管理和排队机制
- 自动生成性能图表

### 🔧 服务增强
- 内置排队机制，防止服务过载
- 性能监控接口
- 动态配置调整

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动向量服务

```bash
cd embedding
python embedding_server.py --host 0.0.0.0 --port 36001 --embedding-model BAAI/bge-large-zh
```

### 3. 运行完整测试套件

```bash
python run_tests.py --url http://localhost:36001
```

### 4. 查看测试结果

测试完成后，结果将保存在 `test_results/` 目录中：
- `comprehensive_test_report_*.json` - 综合测试报告
- `charts/` - 性能图表
- 各种详细测试数据文件

## 详细使用说明

### 压力测试

单独运行压力测试：

```bash
python embedding_stress_test.py \
    --url http://localhost:36001 \
    --max-concurrent 50 \
    --step-size 5 \
    --requests-per-step 50
```

参数说明：
- `--url`: 服务地址
- `--max-concurrent`: 最大并发数
- `--step-size`: 并发度递增步长
- `--requests-per-step`: 每个并发度的请求数

### 客户端模拟

单独运行客户端模拟：

```bash
python client_simulation.py \
    --url http://localhost:36001 \
    --users 100 \
    --duration 300 \
    --arrival-pattern uniform
```

参数说明：
- `--users`: 模拟用户数
- `--duration`: 会话持续时间（秒）
- `--arrival-pattern`: 用户到达模式（uniform/burst/gradual）

### 服务配置

可以通过API动态调整服务配置：

```bash
# 查看服务状态
curl http://localhost:36001/stats

# 调整队列大小和并发数
curl -X POST http://localhost:36001/config \
    -H "Content-Type: application/json" \
    -d '{"max_queue_size": 200, "max_concurrent_requests": 20}'
```

## 测试结果解读

### 压力测试结果

```json
{
  "summary": {
    "total_requests": 1000,
    "successful_requests": 950,
    "success_rate": 0.95,
    "avg_response_time": 1.234,
    "p95_response_time": 2.456,
    "p99_response_time": 3.789
  },
  "recommendations": {
    "optimal_concurrency": 25,
    "max_throughput_per_instance": 15.6,
    "estimated_instances_for_2000_users": 3
  }
}
```

### 客户端模拟结果

```json
{
  "analysis": {
    "total_users": 100,
    "success_rate": 0.92,
    "user_satisfaction_score": 0.85,
    "average_response_time": 1.567
  }
}
```

### 容量规划建议

基于测试结果，系统会自动生成：

1. **单实例容量评估**
   - 最大吞吐量
   - 最佳并发度
   - 推荐队列大小

2. **扩缩容建议**
   - 不同用户规模下的实例数需求
   - 自动扩缩容规则
   - 安全边际建议

3. **性能优化建议**
   - 响应时间优化
   - 成功率提升
   - 用户体验改善

## 架构说明

### 服务端增强

原有的embedding服务被增强了以下功能：

1. **排队机制**: 当请求超过处理能力时自动排队
2. **性能监控**: 实时统计处理性能和队列状态
3. **动态配置**: 支持运行时调整参数
4. **健康检查**: 提供服务健康状态接口

### 测试工具架构

```
run_tests.py (主控制器)
├── embedding_stress_test.py (压力测试)
├── client_simulation.py (客户端模拟)
└── embedding/embedding_server.py (增强服务)
```

## 最佳实践

### 1. 测试环境准备
- 确保测试环境与生产环境配置相似
- 使用相同的模型和硬件配置
- 预热模型，避免冷启动影响

### 2. 测试策略
- 从小负载开始，逐步增加
- 测试多种场景（正常、高峰、突发）
- 关注长时间运行的稳定性

### 3. 结果分析
- 重点关注P95、P99响应时间
- 监控资源使用情况（CPU、内存、GPU）
- 考虑用户体验指标

### 4. 容量规划
- 预留足够的安全边际（建议30%）
- 考虑业务增长预期
- 制定自动扩缩容策略

## 故障排除

### 常见问题

1. **服务无法启动**
   - 检查端口是否被占用
   - 确认模型文件路径正确
   - 检查依赖包是否完整安装

2. **测试连接失败**
   - 确认服务地址和端口正确
   - 检查防火墙设置
   - 验证服务健康状态

3. **性能异常**
   - 检查系统资源使用情况
   - 确认模型是否正确加载
   - 查看服务日志排查错误

### 日志分析

服务运行时会输出详细日志，包括：
- 请求处理时间
- 队列状态变化
- 错误信息和异常

## 扩展功能

### 自定义测试数据

可以修改测试脚本中的测试数据：

```python
# 在 embedding_stress_test.py 中
def _generate_test_data(self):
    # 添加您的自定义测试文本
    return ["您的测试文本1", "您的测试文本2", ...]
```

### 添加新的性能指标

可以在 `PerformanceMonitor` 类中添加新的监控指标：

```python
class PerformanceMonitor:
    def __init__(self):
        # 添加新的指标
        self.custom_metric = 0
```

### 集成监控系统

可以将性能数据发送到外部监控系统：

```python
# 在服务中添加监控数据推送
async def push_metrics_to_monitoring_system(metrics):
    # 实现您的监控系统集成
    pass
```

## 许可证

本项目采用 MIT 许可证。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个测试工具。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至项目维护者
