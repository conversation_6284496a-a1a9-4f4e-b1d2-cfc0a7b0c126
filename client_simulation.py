#!/usr/bin/env python3
"""
客户端模拟脚本
模拟真实用户的使用模式，测试排队机制和服务稳定性
"""

import asyncio
import aiohttp
import argparse
import json
import time
import random
from typing import List, Dict, Any
from dataclasses import dataclass
import logging
import numpy as np

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class UserSession:
    """用户会话数据类"""
    user_id: str
    session_start: float
    requests_sent: int = 0
    successful_requests: int = 0
    total_wait_time: float = 0.0
    total_response_time: float = 0.0

class ClientSimulator:
    """客户端模拟器"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.user_sessions: Dict[str, UserSession] = {}
        self.test_queries = self._load_test_queries()
        
    def _load_test_queries(self) -> List[str]:
        """加载测试查询数据"""
        queries = [
            "如何提高工作效率？",
            "人工智能的发展趋势是什么？",
            "机器学习算法有哪些类型？",
            "深度学习在图像识别中的应用",
            "自然语言处理的核心技术",
            "云计算的优势和挑战",
            "大数据分析的方法和工具",
            "区块链技术的应用场景",
            "物联网设备的安全问题",
            "量子计算的基本原理",
            "软件开发的最佳实践",
            "数据库设计的原则",
            "网络安全防护策略",
            "移动应用开发技术",
            "前端框架的选择标准",
            "后端架构设计模式",
            "微服务架构的优缺点",
            "容器化技术的应用",
            "DevOps实践指南",
            "敏捷开发方法论"
        ]
        
        # 扩展查询，增加变化
        extended_queries = []
        for query in queries:
            extended_queries.append(query)
            extended_queries.append(f"请详细解释{query}")
            extended_queries.append(f"关于{query}的最新研究")
            
        return extended_queries

    async def simulate_user_behavior(self, user_id: str, session_duration: int = 300,
                                   request_interval_range: tuple = (5, 30)) -> UserSession:
        """模拟单个用户的行为"""
        session = UserSession(user_id=user_id, session_start=time.time())
        self.user_sessions[user_id] = session
        
        session_end_time = session.session_start + session_duration
        
        async with aiohttp.ClientSession() as http_session:
            while time.time() < session_end_time:
                # 随机选择查询
                query = random.choice(self.test_queries)
                
                # 发送请求
                await self._send_user_request(http_session, session, query)
                
                # 随机等待间隔（模拟用户思考时间）
                wait_time = random.uniform(*request_interval_range)
                await asyncio.sleep(wait_time)
        
        return session

    async def _send_user_request(self, session: aiohttp.ClientSession, 
                               user_session: UserSession, query: str):
        """发送用户请求"""
        start_time = time.time()
        user_session.requests_sent += 1
        
        try:
            payload = {
                "input": [query],
                "model": "bge-m3"
            }
            
            async with session.post(
                f"{self.base_url}/embeddings",
                json=payload,
                timeout=aiohttp.ClientTimeout(total=60)  # 增加超时时间以处理排队
            ) as response:
                end_time = time.time()
                response_time = end_time - start_time
                
                if response.status == 200:
                    user_session.successful_requests += 1
                    user_session.total_response_time += response_time
                    logger.debug(f"用户 {user_session.user_id} 请求成功，响应时间: {response_time:.3f}s")
                elif response.status == 503:
                    # 服务繁忙，记录等待时间
                    user_session.total_wait_time += response_time
                    logger.warning(f"用户 {user_session.user_id} 请求被拒绝（服务繁忙）")
                else:
                    logger.error(f"用户 {user_session.user_id} 请求失败: HTTP {response.status}")
                    
        except asyncio.TimeoutError:
            logger.error(f"用户 {user_session.user_id} 请求超时")
        except Exception as e:
            logger.error(f"用户 {user_session.user_id} 请求异常: {str(e)}")

    async def simulate_multiple_users(self, num_users: int, session_duration: int = 300,
                                    arrival_pattern: str = "uniform") -> List[UserSession]:
        """模拟多个用户同时使用"""
        logger.info(f"开始模拟 {num_users} 个用户，会话时长: {session_duration}s")
        
        tasks = []
        
        if arrival_pattern == "uniform":
            # 均匀到达
            for i in range(num_users):
                user_id = f"user_{i:04d}"
                task = self.simulate_user_behavior(user_id, session_duration)
                tasks.append(task)
                
        elif arrival_pattern == "burst":
            # 突发到达（模拟高峰期）
            for i in range(num_users):
                user_id = f"user_{i:04d}"
                # 在前30秒内集中到达
                delay = random.uniform(0, 30)
                task = self._delayed_user_simulation(user_id, session_duration, delay)
                tasks.append(task)
                
        elif arrival_pattern == "gradual":
            # 渐进到达
            for i in range(num_users):
                user_id = f"user_{i:04d}"
                # 在整个测试期间逐渐到达
                delay = (session_duration * 0.3) * (i / num_users)
                task = self._delayed_user_simulation(user_id, session_duration, delay)
                tasks.append(task)
        
        # 等待所有用户会话完成
        sessions = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤有效会话
        valid_sessions = [s for s in sessions if isinstance(s, UserSession)]
        logger.info(f"完成用户模拟，有效会话: {len(valid_sessions)}")
        
        return valid_sessions

    async def _delayed_user_simulation(self, user_id: str, session_duration: int, 
                                     delay: float) -> UserSession:
        """延迟启动用户模拟"""
        await asyncio.sleep(delay)
        return await self.simulate_user_behavior(user_id, session_duration)

    async def monitor_service_stats(self, duration: int = 300, interval: int = 10) -> List[Dict]:
        """监控服务统计信息"""
        stats_history = []
        end_time = time.time() + duration
        
        while time.time() < end_time:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        f"{self.base_url}/stats",
                        timeout=aiohttp.ClientTimeout(total=5)
                    ) as response:
                        if response.status == 200:
                            stats = await response.json()
                            stats['timestamp'] = time.time()
                            stats_history.append(stats)
                            logger.info(f"服务统计: 队列大小={stats.get('current_queue_size', 0)}, "
                                      f"处理中请求={stats.get('processing_requests', 0)}, "
                                      f"总请求数={stats.get('total_requests', 0)}")
            except Exception as e:
                logger.error(f"获取服务统计失败: {str(e)}")
            
            await asyncio.sleep(interval)
        
        return stats_history

    def analyze_user_sessions(self, sessions: List[UserSession]) -> Dict[str, Any]:
        """分析用户会话数据"""
        if not sessions:
            return {}
        
        total_requests = sum(s.requests_sent for s in sessions)
        total_successful = sum(s.successful_requests for s in sessions)
        success_rate = total_successful / total_requests if total_requests > 0 else 0
        
        # 计算平均响应时间
        total_response_time = sum(s.total_response_time for s in sessions)
        avg_response_time = total_response_time / total_successful if total_successful > 0 else 0
        
        # 计算用户满意度（基于成功率和响应时间）
        user_satisfaction_scores = []
        for session in sessions:
            if session.requests_sent > 0:
                session_success_rate = session.successful_requests / session.requests_sent
                session_avg_response = (session.total_response_time / session.successful_requests 
                                      if session.successful_requests > 0 else float('inf'))
                
                # 满意度评分：成功率权重70%，响应时间权重30%
                response_score = max(0, 1 - (session_avg_response - 1) / 10)  # 1秒内满分，超过11秒为0
                satisfaction = session_success_rate * 0.7 + response_score * 0.3
                user_satisfaction_scores.append(satisfaction)
        
        avg_satisfaction = np.mean(user_satisfaction_scores) if user_satisfaction_scores else 0
        
        return {
            "total_users": len(sessions),
            "total_requests": total_requests,
            "successful_requests": total_successful,
            "success_rate": success_rate,
            "average_response_time": avg_response_time,
            "user_satisfaction_score": avg_satisfaction,
            "users_with_zero_success": sum(1 for s in sessions if s.successful_requests == 0),
            "average_requests_per_user": total_requests / len(sessions),
            "total_wait_time": sum(s.total_wait_time for s in sessions)
        }

    def save_simulation_results(self, sessions: List[UserSession], 
                              stats_history: List[Dict], 
                              analysis: Dict[str, Any],
                              filename: str = "client_simulation_results.json"):
        """保存模拟结果"""
        results = {
            "analysis": analysis,
            "stats_history": stats_history,
            "session_summary": [
                {
                    "user_id": s.user_id,
                    "requests_sent": s.requests_sent,
                    "successful_requests": s.successful_requests,
                    "success_rate": s.successful_requests / s.requests_sent if s.requests_sent > 0 else 0,
                    "avg_response_time": s.total_response_time / s.successful_requests if s.successful_requests > 0 else 0,
                    "total_wait_time": s.total_wait_time
                }
                for s in sessions
            ]
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        logger.info(f"模拟结果已保存到 {filename}")

async def main():
    parser = argparse.ArgumentParser(description="客户端模拟测试")
    parser.add_argument("--url", default="http://localhost:36001", 
                       help="服务URL")
    parser.add_argument("--users", type=int, default=50,
                       help="模拟用户数")
    parser.add_argument("--duration", type=int, default=300,
                       help="会话持续时间（秒）")
    parser.add_argument("--arrival-pattern", choices=["uniform", "burst", "gradual"],
                       default="uniform", help="用户到达模式")
    parser.add_argument("--output", default="client_simulation_results.json",
                       help="结果输出文件")
    
    args = parser.parse_args()
    
    # 创建模拟器
    simulator = ClientSimulator(args.url)
    
    # 检查服务是否可用
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{args.url}/health", timeout=aiohttp.ClientTimeout(total=5)) as response:
                if response.status != 200:
                    logger.error("服务不可用")
                    return
    except:
        logger.error("无法连接到服务")
        return
    
    logger.info("开始客户端模拟测试...")
    
    # 启动服务监控
    monitor_task = asyncio.create_task(
        simulator.monitor_service_stats(args.duration + 60)
    )
    
    # 运行用户模拟
    sessions = await simulator.simulate_multiple_users(
        args.users, args.duration, args.arrival_pattern
    )
    
    # 等待监控完成
    await asyncio.sleep(10)  # 额外等待时间
    monitor_task.cancel()
    
    try:
        stats_history = await monitor_task
    except asyncio.CancelledError:
        stats_history = []
    
    # 分析结果
    analysis = simulator.analyze_user_sessions(sessions)
    
    # 打印结果摘要
    print("\n" + "="*50)
    print("客户端模拟测试结果")
    print("="*50)
    print(f"模拟用户数: {analysis['total_users']}")
    print(f"总请求数: {analysis['total_requests']}")
    print(f"成功请求数: {analysis['successful_requests']}")
    print(f"成功率: {analysis['success_rate']:.2%}")
    print(f"平均响应时间: {analysis['average_response_time']:.3f}s")
    print(f"用户满意度评分: {analysis['user_satisfaction_score']:.2f}/1.0")
    print(f"零成功用户数: {analysis['users_with_zero_success']}")
    print(f"平均每用户请求数: {analysis['average_requests_per_user']:.1f}")
    
    # 保存结果
    simulator.save_simulation_results(sessions, stats_history, analysis, args.output)
    print(f"\n详细结果已保存到: {args.output}")

if __name__ == "__main__":
    asyncio.run(main())
