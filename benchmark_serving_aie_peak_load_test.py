"""Benchmark online serving throughput.
"""

import argparse
import asyncio
import datetime
import json
import random
import statistics
import time
from typing import AsyncGenerator, <PERSON>, Tuple

import numpy as np
import requests
from http import HTTPStatus
import aiohttp
import numpy as np

REQUEST_LATENCY: List[Tuple[int, int, float]] = []
MAGIC_PROMPT = "OA"
TOTAL_INTERVAL: List[float] = []
ZTE_PROMPT_LEN = 69
QUESTION = " Do not code, write all the numbers between 1-10000"
latency_list = []
mean_latency_list = []
input_tokens_num_list = []
output_tokens_num_list = []
only_classify = True


def create_fake_requests(
    num_requests: int,
    prompt_len: int,
    output_len: int,
) -> List[Tuple[str, int, int]]:
    fake_data: List[Tuple[str, int, int]] = []
    for _ in range(num_requests):
        fake_data.append(
            (
                MAGIC_PROMPT * (prompt_len - ZTE_PROMPT_LEN) + QUESTION,
                prompt_len,
                output_len,
            )
        )
    return fake_data


def create_requests_from_file(
    num_requests: int,
    output_len: int,
    file_path: str,
) -> List[Tuple[str, int, int]]:
    real_data: List[Tuple[str, int, int]] = []

    prompts = []
    with open("risk.json", "r") as f:
        input_list = json.load(f)
        input_list = input_list[:]
        for line in input_list:
            prompts.append(line["question"])
            # prompts.append(line["input_tokens"])
            prompts.append(len(line["question"]))
    # Read prompts from file
    # with open(file_path, "r") as f:
    #     for line in f:
    #         print(line)
    #         prompt = json.loads(line)[0]["prompt"]
    #         prompt_len = len(json.loads(line)[0]["tokens"])
    #         prompts.append(prompt)
    #         prompts.append(prompt_len)

    for n in range(num_requests):
        if n >= len(prompts) / 2:
            n = int(n % (len(prompts) / 2))
        real_data.append((prompts[2 * n], prompts[2 * n + 1], output_len))
        input_tokens_num_list.append(float(prompts[2 * n + 1]))
    print(f"mean input num:{statistics.mean(input_tokens_num_list)}")
    return real_data


async def get_request(
    input_requests: List[Tuple[str, int, int]],
    request_rate: float,
) -> AsyncGenerator[Tuple[str, int, int], None]:
    input_requests = iter(input_requests)
    cnt = 0
    for request in input_requests:
        yield request

        if request_rate == float("inf"):
            # If the request rate is infinity, then we don't need to wait.
            continue
        # Sample the request interval from the exponential distribution.
        interval = np.random.exponential(1.0 / request_rate)
        print(
            f"Send request id: {cnt}, sleep {interval:.4f} sec...", flush=True)
        cnt += 1
        TOTAL_INTERVAL.append(interval)
        # The next request will be sent after the interval.
        await asyncio.sleep(interval)


async def send_request(
    backend: str,
    api_url: str,
    prompt: str,
    prompt_len: int,
    output_len: int,
    best_of: int,
    use_beam_search: bool,
    real_prompts: str,
    count: int,
) -> None:
    start_time = time.time()
    get_time = 0.0
    end_time = 0.0
    print(f"--- {count} Start time: {start_time}")
    headers = {"Content-Type": "application/json"}
    if backend == "vllm":
        pload = {
            "messages": [
                {
                    "role": "user",
                    "content": prompt,
                },
            ],
            "max_tokens": output_len,
            "stream": True,
            "user_id": "10307260",
        }
        # pload = {"instruction": prompt}
        # pload = {
        #     "token": "eFE0aP4YWjRyvaDb",
        #     "content": prompt,
        #     "use_stream": True,
        #     "no_suggest": False,
        # }
    else:
        raise ValueError(f"Unknown backend: {backend}")

    timeout = aiohttp.ClientTimeout(total=3 * 3600)
    is_printed = False
    async with aiohttp.ClientSession(timeout=timeout) as session:
        output_tokens = output_len
        while True:
            async with session.post(api_url, headers=headers, json=pload) as response:
                chunks = []
                # print("res:{response.status}")
                async for chunk, _ in response.content.iter_chunks():
                    chunk_data = chunk.decode("utf-8")[6:].strip()
                    if chunk_data:
                        if not is_printed:
                            get_time = time.time()
                            mid_latency = (get_time - start_time) * 1
                            print(f"    --- {count} Get time: {get_time}")
                            is_printed = True
                            # 解析 JSON 对象
                            if only_classify:
                                try:
                                    json_obj = json.loads(chunk_data)
                                except Exception as e:
                                    print(str(e))
                                    print(chunk_data)
                                    continue

                                # 处理接收到的 JSON 对象
                                output = json_obj["output"][0]["text"]
                                # output = chunk_data
                                chunks.append(output)

                        if chunk_data[-1] == "]":
                            chunks.append(chunk_data[-6:])
                            break
                        try:
                            # 解析 JSON 对象
                            json_obj = json.loads(chunk_data)
                            output_tokens = json_obj["usage"]["output_tokens"]
                        # 处理接收到的 JSON 对象
                        except Exception:
                            continue

            if chunks:
                end_time = time.time()
                single_latency = (end_time - start_time) * 1
                latency = single_latency - mid_latency
                mean_latency = latency / output_tokens
                output_tokens_num_list.append(output_tokens)
                print(
                    f"        --- {count} End time: {end_time}  get_delay: {mid_latency} s, end_delay: {single_latency} s, latency: {latency} s,mean_latency:{mean_latency}"
                )
                latency_list.append(mid_latency)
                mean_latency_list.append(mean_latency)
                break


async def benchmark(
    backend: str,
    api_url: str,
    input_requests: List[Tuple[str, int, int]],
    best_of: int,
    use_beam_search: bool,
    request_rate: float,
    real_prompts: str,
) -> None:
    tasks: List[asyncio.Task] = []
    count = 0
    async for request in get_request(input_requests, request_rate):
        prompt, prompt_len, output_len = request
        task = asyncio.create_task(
            send_request(
                backend,
                api_url,
                prompt,
                prompt_len,
                output_len,
                best_of,
                use_beam_search,
                real_prompts,
                count,
            )
        )
        tasks.append(task)
        count += 1
    await asyncio.gather(*tasks)


def main(args: argparse.Namespace):
    current_time = datetime.datetime.now()
    adjusted_time = (
        current_time
        - datetime.timedelta(hours=8)
        + datetime.timedelta(minutes=12)
        + datetime.timedelta(seconds=27)
    )
    formatted_time = adjusted_time.strftime("%Y-%m-%d %H:%M:%S %Z")
    print(f"{formatted_time}")

    print(args)
    random.seed(args.seed)
    np.random.seed(args.seed)

    api_url = ""
    if "None" in args.real_prompts:
        input_requests = create_fake_requests(
            args.num_prompts, args.input_len, args.output_len
        )
    else:
        input_requests = create_requests_from_file(
            args.num_prompts, args.output_len, args.real_prompts
        )
    benchmark_start_time = time.time()
    asyncio.run(
        benchmark(
            args.backend,
            api_url,
            input_requests,
            args.best_of,
            args.use_beam_search,
            args.request_rate,
            args.real_prompts,
        )
    )
    benchmark_end_time = time.time()
    benchmark_time = benchmark_end_time - benchmark_start_time

    current_time = datetime.datetime.now()
    adjusted_time = (
        current_time
        - datetime.timedelta(hours=8)
        + datetime.timedelta(minutes=12)
        + datetime.timedelta(seconds=27)
    )
    formatted_time = adjusted_time.strftime("%Y-%m-%d %H:%M:%S %Z")
    print(f"\n{formatted_time}")
    print(f"mean output tokens {statistics.mean(output_tokens_num_list)}")
    print(f"Mean latency: {statistics.mean(latency_list)} seconds")
    print(f"Mean token latency: {statistics.mean(mean_latency_list)} seconds")
    print(f"Total time: {benchmark_time:.2f} sec")
    print(
        f"Throughput: {args.num_prompts / benchmark_time:.2f} requests/sec, {(args.input_len + args.output_len) * args.num_prompts / benchmark_time:.2f} tokens/sec"
    )
    percentile_90 = np.percentile(latency_list, 90)
    print("第90个百分位数是:", percentile_90)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Benchmark the online serving throughput."
    )
    parser.add_argument("--backend", type=str,
                        default="vllm", choices=["vllm", "tgi"])
    parser.add_argument(
        "--real-prompts", type=str, default="None", help="Use real prompts as input."
    )
    parser.add_argument(
        "--best-of",
        type=int,
        default=1,
        help="Generates `best_of` sequences per prompt and " "returns the best one.",
    )
    parser.add_argument("--use-beam-search", action="store_true")
    parser.add_argument(
        "--num-prompts", type=int, default=1000, help="Number of prompts to process."
    )
    parser.add_argument(
        "--input-len", type=int, default=1024, help="Length of prompts token ids."
    )
    parser.add_argument(
        "--output-len",
        type=int,
        default=1024,
        help="Length of generated new token ids.",
    )
    parser.add_argument(
        "--request-rate",
        type=float,
        default=float("inf"),
        help="Number of requests per second. If this is inf, "
        "then all the requests are sent at time 0. "
        "Otherwise, we use Poisson process to synthesize "
        "the request arrival times.",
    )
    parser.add_argument("--seed", type=int, default=0)
    args = parser.parse_args()
    main(args)
