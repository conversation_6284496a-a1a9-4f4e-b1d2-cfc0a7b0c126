# 向量模型服务测试工具 Makefile

.PHONY: help install start stop status test quick-test stress-test client-sim full-test queue-opt clean

# 默认目标
help:
	@echo "向量模型服务测试工具"
	@echo ""
	@echo "可用命令:"
	@echo "  install      - 安装依赖包"
	@echo "  start        - 启动服务"
	@echo "  start-daemon - 后台启动服务"
	@echo "  stop         - 停止服务"
	@echo "  status       - 查看服务状态"
	@echo "  quick-test   - 运行快速测试"
	@echo "  stress-test  - 运行压力测试"
	@echo "  client-sim   - 运行客户端模拟"
	@echo "  queue-opt    - 运行队列大小优化测试"
	@echo "  full-test    - 运行完整测试套件"
	@echo "  clean        - 清理测试结果"
	@echo "  help         - 显示此帮助信息"

# 安装依赖
install:
	@echo "安装Python依赖包..."
	pip install -r requirements.txt
	@echo "依赖安装完成"

# 启动服务（前台）
start:
	@echo "启动向量模型服务（前台模式）..."
	./start_service.sh

# 启动服务（后台）
start-daemon:
	@echo "启动向量模型服务（后台模式）..."
	./start_service.sh --daemon

# 停止服务
stop:
	@echo "停止向量模型服务..."
	./start_service.sh --stop

# 查看服务状态
status:
	@echo "查看服务状态..."
	./start_service.sh --status

# 快速测试
quick-test:
	@echo "运行快速测试..."
	python3 quick_test.py

# 压力测试
stress-test:
	@echo "运行压力测试..."
	python3 embedding_stress_test.py

# 客户端模拟
client-sim:
	@echo "运行客户端模拟测试..."
	python3 client_simulation.py

# 队列大小优化测试
queue-opt:
	@echo "运行队列大小优化测试..."
	python3 queue_size_optimizer.py

# 完整测试套件
full-test:
	@echo "运行完整测试套件..."
	python3 run_tests.py

# 清理测试结果
clean:
	@echo "清理测试结果..."
	rm -rf test_results/
	rm -f *.json
	rm -f /tmp/embedding_server_*.pid
	rm -f /tmp/embedding_server_*.log
	@echo "清理完成"

# 开发环境设置
dev-setup: install
	@echo "设置开发环境..."
	mkdir -p test_results
	@echo "开发环境设置完成"

# 检查服务健康状态
health-check:
	@echo "检查服务健康状态..."
	curl -s http://localhost:36001/health || echo "服务不可用"

# 查看服务统计
stats:
	@echo "查看服务统计信息..."
	curl -s http://localhost:36001/stats | python3 -m json.tool || echo "无法获取统计信息"

# 重启服务
restart: stop start-daemon
	@echo "服务重启完成"

# 查看日志
logs:
	@echo "查看服务日志..."
	tail -f /tmp/embedding_server_36001.log

# 运行特定的测试场景
test-burst:
	@echo "运行突发负载测试..."
	python3 client_simulation.py --arrival-pattern burst --users 50

test-gradual:
	@echo "运行渐进负载测试..."
	python3 client_simulation.py --arrival-pattern gradual --users 100

test-high-load:
	@echo "运行高负载测试..."
	python3 embedding_stress_test.py --max-concurrent 100

# 生成测试报告
report:
	@echo "生成测试报告..."
	python3 -c "
import json
import glob
import os
from datetime import datetime

print('最近的测试结果:')
files = glob.glob('test_results/*.json')
if files:
    latest = max(files, key=os.path.getctime)
    print(f'最新报告: {latest}')
    with open(latest, 'r', encoding='utf-8') as f:
        data = json.load(f)
        if 'summary' in data:
            print(f'成功率: {data[\"summary\"].get(\"success_rate\", 0):.2%}')
            print(f'平均响应时间: {data[\"summary\"].get(\"avg_response_time\", 0):.3f}s')
else:
    print('没有找到测试结果文件')
"

# Docker相关命令（如果需要）
docker-build:
	@echo "构建Docker镜像..."
	docker build -t embedding-service .

docker-run:
	@echo "运行Docker容器..."
	docker run -d -p 36001:36001 --name embedding-service embedding-service

docker-stop:
	@echo "停止Docker容器..."
	docker stop embedding-service
	docker rm embedding-service
