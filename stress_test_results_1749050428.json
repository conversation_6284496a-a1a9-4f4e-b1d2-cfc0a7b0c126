{"summary": {"total_requests": 922, "successful_requests": 748, "timeout_requests": 112, "success_rate": 0.8112798264642083, "timeout_rate": 0.12147505422993492, "avg_response_time": 1.3682926246189178, "median_response_time": 1.1692845821380615, "p95_response_time": 2.895646595954895, "p99_response_time": 3.941053576469411, "min_response_time": 0.12863659858703613, "max_response_time": 9.461594104766846}, "by_rps": {"5": {"target_rps": 5, "total_requests": 334, "successful_requests": 331, "timeout_requests": 2, "success_rate": 0.9910179640718563, "timeout_rate": 0.005988023952095809, "avg_response_time": 0.3517129903833672, "p95_response_time": 0.7773920297622681, "actual_throughput": 4.741502269281692}, "10": {"target_rps": 10, "total_requests": 588, "successful_requests": 417, "timeout_requests": 110, "success_rate": 0.7091836734693877, "timeout_rate": 0.1870748299319728, "avg_response_time": 2.1752179457986953, "p95_response_time": 2.9592330932617186, "actual_throughput": 6.949417113079933}}, "recommendations": {"max_sustainable_rps": 5, "max_throughput_per_instance": 4.741502269281692, "estimated_instances_for_2000_users": 8, "recommended_safe_rps": 4, "safety_margin": "建议使用80%的最大承载能力作为安全运行点", "queue_size_recommendations": {"max_queue_size": 10, "explanation": "队列大小应能缓冲短期突发流量，建议设置为最大RPS的2倍"}, "monitoring_recommendations": ["监控每秒请求数，确保不超过 4 RPS", "监控超时率，确保低于5%", "监控队列长度，避免积压过多请求", "监控响应时间P95，确保在可接受范围内", "设置自动扩缩容策略，基于队列长度和响应时间", "定期进行吞吐量测试验证容量"], "scaling_strategy": {"scale_up_threshold": "队列长度超过 5 或响应时间P95超过5秒", "scale_down_threshold": "队列长度低于 2 且响应时间P95低于2秒", "min_instances": 2, "max_instances": 10}}, "max_sustainable_rps": 5}