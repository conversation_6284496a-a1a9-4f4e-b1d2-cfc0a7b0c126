#!/usr/bin/env python3
"""
向量模型服务压力测试工具
用于测试向量服务的最大承载能力和性能指标
"""

import asyncio
import aiohttp
import argparse
import json
import time
import statistics
import random
from typing import List, Dict, Any
from dataclasses import dataclass
import numpy as np
from concurrent.futures import ThreadPoolExecutor
import logging
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TestResult:
    """测试结果数据类"""
    request_id: str
    start_time: float
    end_time: float
    response_time: float
    success: bool
    error_message: str = ""
    queue_time: float = 0.0
    processing_time: float = 0.0

class EmbeddingStressTest:
    """向量服务压力测试类"""
    
    def __init__(self, base_url: str, max_concurrent: int = 50):
        self.base_url = base_url.rstrip('/')
        self.max_concurrent = max_concurrent
        self.results: List[TestResult] = []
        self.test_data = self._generate_test_data()
        
    def _generate_test_data(self) -> List[str]:
        """生成代码片段测试数据"""
        logger.info("生成代码片段测试数据...")
        
        # 从代码文件中读取真实代码片段
        code_files = {
            "python": ["token_analysis/code/repo_understander.py"],
            "typescript": ["token_analysis/code/index.ts"],
            "c": ["token_analysis/code/ikev2_format.c"] if os.path.exists("token_analysis/code/ikev2_format.c") else []
        }
        
        code_snippets = []
        window_size = 50  # 每个窗口50行，与token_analysis一致
        
        # 读取并切分代码文件
        for lang, file_paths in code_files.items():
            for file_path in file_paths:
                if not os.path.exists(file_path):
                    logger.warning(f"文件不存在: {file_path}")
                    continue
                    
                try:
                    with open(file_path, 'r', encoding='utf-8') as file:
                        lines = file.readlines()
                        
                        # 如果文件行数小于window_size，则使用整个文件
                        if len(lines) <= window_size:
                            snippet = ''.join(lines)
                            code_snippets.append((snippet, lang, file_path))
                        else:
                            # 使用滑动窗口生成代码片段
                            for i in range(0, len(lines) - window_size + 1, window_size // 2):  # 50%重叠
                                window = lines[i:i + window_size]
                                snippet = ''.join(window)
                                code_snippets.append((snippet, lang, f"{file_path}:{i+1}-{i+window_size}"))
                                
                                # 控制每个文件的片段数量，避免数据过多
                                if len(code_snippets) % 10 == 0:
                                    logger.info(f"已生成 {len(code_snippets)} 个代码片段")
                                    
                except Exception as e:
                    logger.error(f"读取文件 {file_path} 时出错: {str(e)}")
        
        # 如果没有足够的代码片段，添加一些备用数据
        if len(code_snippets) < 10:
            logger.warning("代码片段不足，添加备用数据")
            backup_snippets = [
                "def calculate_embeddings(text_list):\n    return model.encode(text_list)",
                "async function processRequest(req, res) {\n    const data = await getEmbeddings(req.body.text);\n    return res.json(data);\n}",
                "class EmbeddingModel:\n    def __init__(self, model_name):\n        self.model = AutoModel.from_pretrained(model_name)\n\n    def encode(self, texts):\n        return self.model(texts)"
            ]
            for i, snippet in enumerate(backup_snippets):
                code_snippets.append((snippet, "python", f"backup_snippet_{i}"))
        
        # 扩展数据集，确保有足够的测试数据
        if len(code_snippets) < 30:
            # 复制现有片段直到达到所需数量
            original_snippets = code_snippets.copy()
            while len(code_snippets) < 30:
                code_snippets.extend(original_snippets)
        
        logger.info(f"共生成 {len(code_snippets)} 个代码片段用于测试")
        return [snippet for snippet, _, _ in code_snippets]

    async def send_single_request(self, session: aiohttp.ClientSession, 
                                code_snippet: str, request_id: str) -> TestResult:
        """发送单个代码片段向量化请求"""
        start_time = time.time()
        
        try:
            # 使用与token_analysis/cal_tokens.py相同的模型
            payload = {
                "input": [code_snippet],
                "model": "bge-m3",  # 使用与服务相同的模型名称
                "encoding_format": "float"  # 确保返回浮点数向量
            }
            
            async with session.post(
                f"{self.base_url}/embeddings",
                json=payload,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                end_time = time.time()
                response_time = end_time - start_time
                
                if response.status == 200:
                    data = await response.json()
                    return TestResult(
                        request_id=request_id,
                        start_time=start_time,
                        end_time=end_time,
                        response_time=response_time,
                        success=True
                    )
                else:
                    error_text = await response.text()
                    return TestResult(
                        request_id=request_id,
                        start_time=start_time,
                        end_time=end_time,
                        response_time=response_time,
                        success=False,
                        error_message=f"HTTP {response.status}: {error_text}"
                    )
                    
        except Exception as e:
            end_time = time.time()
            return TestResult(
                request_id=request_id,
                start_time=start_time,
                end_time=end_time,
                response_time=end_time - start_time,
                success=False,
                error_message=str(e)
            )

    async def run_concurrent_test(self, num_requests: int, 
                                concurrent_level: int) -> List[TestResult]:
        """运行并发测试"""
        logger.info(f"开始并发测试: {num_requests} 个请求, 并发度: {concurrent_level}")
        
        semaphore = asyncio.Semaphore(concurrent_level)
        
        async def bounded_request(session, text, request_id):
            async with semaphore:
                return await self.send_single_request(session, text, request_id)
        
        async with aiohttp.ClientSession() as session:
            tasks = []
            for i in range(num_requests):
                text = random.choice(self.test_data)
                request_id = f"req_{i:06d}"
                task = bounded_request(session, text, request_id)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
        # 过滤异常结果
        valid_results = [r for r in results if isinstance(r, TestResult)]
        logger.info(f"完成并发测试，有效结果: {len(valid_results)}")
        
        return valid_results

    async def run_gradual_load_test(self, max_concurrent: int, 
                                  step_size: int = 5, 
                                  requests_per_step: int = 50) -> Dict[int, List[TestResult]]:
        """运行渐进式负载测试"""
        logger.info(f"开始渐进式负载测试，最大并发: {max_concurrent}")
        
        results_by_concurrency = {}
        
        for concurrent_level in range(step_size, max_concurrent + 1, step_size):
            logger.info(f"测试并发度: {concurrent_level}")
            
            # 检查服务健康状态
            if not await self.check_service_health():
                logger.warning(f"服务健康检查失败，跳过并发度 {concurrent_level}")
                break
                
            results = await self.run_concurrent_test(requests_per_step, concurrent_level)
            results_by_concurrency[concurrent_level] = results
            
            # 分析当前并发度的结果
            success_rate = sum(1 for r in results if r.success) / len(results)
            avg_response_time = statistics.mean([r.response_time for r in results if r.success])
            
            logger.info(f"并发度 {concurrent_level}: 成功率 {success_rate:.2%}, "
                       f"平均响应时间 {avg_response_time:.3f}s")
            
            # 如果成功率过低，停止测试
            if success_rate < 0.8:
                logger.warning(f"成功率过低 ({success_rate:.2%})，停止测试")
                break
                
            # 等待一段时间让服务恢复
            await asyncio.sleep(2)
            
        return results_by_concurrency

    async def check_service_health(self) -> bool:
        """检查服务健康状态"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/health",
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    return response.status == 200
        except:
            return False

    async def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/stats",
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        return await response.json()
        except:
            pass
        return {}

    def analyze_results(self, results_by_concurrency: Dict[int, List[TestResult]]) -> Dict[str, Any]:
        """分析测试结果"""
        analysis = {
            "summary": {},
            "by_concurrency": {},
            "recommendations": {}
        }
        
        all_results = []
        for results in results_by_concurrency.values():
            all_results.extend(results)
        
        if not all_results:
            return analysis
            
        # 总体统计
        total_requests = len(all_results)
        successful_requests = sum(1 for r in all_results if r.success)
        success_rate = successful_requests / total_requests
        
        successful_results = [r for r in all_results if r.success]
        if successful_results:
            response_times = [r.response_time for r in successful_results]
            analysis["summary"] = {
                "total_requests": total_requests,
                "successful_requests": successful_requests,
                "success_rate": success_rate,
                "avg_response_time": statistics.mean(response_times),
                "median_response_time": statistics.median(response_times),
                "p95_response_time": np.percentile(response_times, 95),
                "p99_response_time": np.percentile(response_times, 99),
                "min_response_time": min(response_times),
                "max_response_time": max(response_times)
            }
        
        # 按并发度分析
        for concurrency, results in results_by_concurrency.items():
            successful = [r for r in results if r.success]
            if successful:
                response_times = [r.response_time for r in successful]
                analysis["by_concurrency"][concurrency] = {
                    "total_requests": len(results),
                    "successful_requests": len(successful),
                    "success_rate": len(successful) / len(results),
                    "avg_response_time": statistics.mean(response_times),
                    "p95_response_time": np.percentile(response_times, 95),
                    "throughput": len(successful) / max(response_times) if response_times else 0
                }
        
        # 找到最佳并发度
        best_concurrency = self._find_optimal_concurrency(analysis["by_concurrency"])
        
        # 生成建议
        analysis["recommendations"] = self._generate_recommendations(
            analysis["by_concurrency"], best_concurrency
        )
        
        return analysis

    def _find_optimal_concurrency(self, by_concurrency: Dict[int, Dict]) -> int:
        """找到最佳并发度"""
        best_score = 0
        best_concurrency = 1
        
        for concurrency, stats in by_concurrency.items():
            # 综合考虑成功率和吞吐量
            score = stats["success_rate"] * stats["throughput"]
            if score > best_score:
                best_score = score
                best_concurrency = concurrency
                
        return best_concurrency

    def _generate_recommendations(self, by_concurrency: Dict[int, Dict], 
                                best_concurrency: int) -> Dict[str, Any]:
        """生成容量规划建议"""
        if not by_concurrency:
            return {}
            
        best_stats = by_concurrency.get(best_concurrency, {})
        
        # 估算单实例最大承载能力
        max_throughput = best_stats.get("throughput", 0)
        
        # 估算2000人所需实例数
        # 假设每人每分钟发送1个请求
        users_per_minute = 2000
        required_instances = max(1, int(users_per_minute / (max_throughput * 60)) + 1)
        
        return {
            "optimal_concurrency": best_concurrency,
            "max_throughput_per_instance": max_throughput,
            "estimated_instances_for_2000_users": required_instances,
            "safety_margin": "建议增加20%的安全边际",
            "monitoring_recommendations": [
                "监控队列长度，避免超过最大队列大小",
                "监控响应时间，确保在可接受范围内",
                "设置自动扩缩容策略",
                "定期进行压力测试验证容量"
            ]
        }

    def save_results(self, analysis: Dict[str, Any], filename: str = None):
        """保存测试结果"""
        if filename is None:
            # 使用时间戳命名文件
            timestamp = int(time.time())
            filename = f"stress_test_results_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)
        logger.info(f"测试结果已保存到 {filename}")

    async def run_realistic_load_test(self, duration: int = 600, 
                                    base_rps: float = 5.0,
                                    peak_factor: float = 3.0,
                                    distribution: str = "poisson") -> List[TestResult]:
        """运行真实负载测试
        
        Args:
            duration: 测试持续时间（秒）
            base_rps: 基础每秒请求数
            peak_factor: 高峰期请求倍数
            distribution: 请求分布类型 (poisson, exponential)
        """
        logger.info(f"开始真实负载测试: 持续{duration}秒, 基础RPS={base_rps}, 峰值因子={peak_factor}")
        
        results = []
        start_time = time.time()
        end_time = start_time + duration
        
        # 创建测试数据池
        test_data_pool = [(random.choice(self.test_data), i, 0) 
                          for i in range(int(duration * base_rps * peak_factor * 1.2))]
        
        # 定义请求生成器
        async def request_generator():
            current_time = time.time()
            elapsed = current_time - start_time
            
            # 模拟一天中的流量波动 (简化模型)
            time_factor = 1.0
            if elapsed < duration * 0.3:  # 前30%时间为上升期
                time_factor = 1.0 + (elapsed / (duration * 0.3)) * (peak_factor - 1.0)
            elif elapsed < duration * 0.5:  # 30%-50%为高峰期
                time_factor = peak_factor
            else:  # 后50%为下降期
                time_factor = peak_factor - (elapsed - duration * 0.5) / (duration * 0.5) * (peak_factor - 1.0)
            
            current_rps = base_rps * time_factor
            
            # 根据分布生成间隔时间
            if distribution == "poisson":
                # 泊松分布的间隔时间是指数分布
                interval = np.random.exponential(1.0 / current_rps)
            elif distribution == "exponential":
                interval = np.random.exponential(1.0 / current_rps)
            else:
                interval = 1.0 / current_rps
                
            return interval
        
        # 执行测试
        async with aiohttp.ClientSession() as session:
            request_id = 0
            next_request_time = time.time()
            
            while time.time() < end_time:
                current_time = time.time()
                
                # 如果到达下一个请求时间
                if current_time >= next_request_time and request_id < len(test_data_pool):
                    # 发送请求
                    text, _, _ = test_data_pool[request_id]
                    task = asyncio.create_task(
                        self.send_single_request(session, text, f"req_{request_id:06d}")
                    )
                    task.add_done_callback(lambda t: results.append(t.result()))
                    
                    # 计算下一个请求时间
                    interval = await request_generator()
                    next_request_time = current_time + interval
                    request_id += 1
                    
                    if request_id % 50 == 0:
                        logger.info(f"已发送 {request_id} 个请求，当前RPS: {1.0/interval:.2f}")
                
                # 短暂休眠避免CPU占用过高
                await asyncio.sleep(0.01)
        
        logger.info(f"测试完成，共发送 {request_id} 个请求")
        return results

    async def run_mixed_pattern_test(self, total_duration: int = 1800) -> Dict[str, List[TestResult]]:
        """运行混合模式测试，模拟一天中的不同时段
        
        Args:
            total_duration: 总测试时间（秒）
        """
        logger.info(f"开始混合模式测试，总持续时间: {total_duration}秒")
        
        results_by_phase = {}
        
        # 1. 低负载阶段 (20% 时间)
        logger.info("阶段1: 低负载阶段 (模拟夜间/凌晨)")
        low_load_results = await self.run_realistic_load_test(
            duration=int(total_duration * 0.2),
            base_rps=2.0,
            peak_factor=1.2,
            distribution="poisson"
        )
        results_by_phase["low_load"] = low_load_results
        
        # 2. 突发负载阶段 (10% 时间)
        logger.info("阶段2: 突发负载阶段 (模拟早高峰)")
        burst_results = await self.run_burst_test(
            num_requests=200,
            burst_duration=int(total_duration * 0.1),
            max_concurrent=30
        )
        results_by_phase["burst_load"] = burst_results
        
        # 3. 稳定高负载阶段 (40% 时间)
        logger.info("阶段3: 稳定高负载阶段 (模拟工作时间)")
        high_load_results = await self.run_realistic_load_test(
            duration=int(total_duration * 0.4),
            base_rps=8.0,
            peak_factor=1.5,
            distribution="poisson"
        )
        results_by_phase["high_load"] = high_load_results
        
        # 4. 波动负载阶段 (30% 时间)
        logger.info("阶段4: 波动负载阶段 (模拟晚间)")
        fluctuating_results = await self.run_fluctuating_test(
            duration=int(total_duration * 0.3),
            base_rps=5.0,
            fluctuation_period=300,  # 5分钟一个周期
            fluctuation_amplitude=0.5  # 上下浮动50%
        )
        results_by_phase["fluctuating_load"] = fluctuating_results
        
        return results_by_phase

    async def run_burst_test(self, num_requests: int, burst_duration: int, 
                           max_concurrent: int) -> List[TestResult]:
        """突发负载测试"""
        logger.info(f"开始突发负载测试: {num_requests}个请求在{burst_duration}秒内")
        
        # 创建测试数据
        texts = [random.choice(self.test_data) for _ in range(num_requests)]
        
        # 控制并发度
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def bounded_request(session, text, request_id):
            async with semaphore:
                return await self.send_single_request(session, text, request_id)
        
        # 执行测试
        async with aiohttp.ClientSession() as session:
            tasks = []
            for i in range(num_requests):
                # 在突发持续时间内均匀分布请求
                delay = random.uniform(0, burst_duration)
                
                async def delayed_request(text, i, delay):
                    await asyncio.sleep(delay)
                    return await bounded_request(session, text, f"burst_{i:06d}")
                
                task = asyncio.create_task(delayed_request(texts[i], i, delay))
                tasks.append(task)
            
            results = await asyncio.gather(*tasks)
        
        return results

    async def run_fluctuating_test(self, duration: int, base_rps: float,
                                 fluctuation_period: int, 
                                 fluctuation_amplitude: float) -> List[TestResult]:
        """波动负载测试"""
        logger.info(f"开始波动负载测试: 持续{duration}秒, 基础RPS={base_rps}")
        
        results = []
        start_time = time.time()
        end_time = start_time + duration
        
        # 创建测试数据池
        test_data_pool = [(random.choice(self.test_data), i, 0) 
                          for i in range(int(duration * base_rps * (1 + fluctuation_amplitude) * 1.2))]
        
        # 执行测试
        async with aiohttp.ClientSession() as session:
            request_id = 0
            next_request_time = time.time()
            
            while time.time() < end_time:
                current_time = time.time()
                
                # 计算当前RPS (基于正弦波形)
                elapsed = current_time - start_time
                phase = (elapsed % fluctuation_period) / fluctuation_period * 2 * np.pi
                factor = 1 + fluctuation_amplitude * np.sin(phase)
                current_rps = base_rps * factor
                
                # 如果到达下一个请求时间
                if current_time >= next_request_time and request_id < len(test_data_pool):
                    # 发送请求
                    text, _, _ = test_data_pool[request_id]
                    task = asyncio.create_task(
                        self.send_single_request(session, text, f"fluct_{request_id:06d}")
                    )
                    task.add_done_callback(lambda t: results.append(t.result()))
                    
                    # 计算下一个请求时间 (泊松分布)
                    interval = np.random.exponential(1.0 / current_rps)
                    next_request_time = current_time + interval
                    request_id += 1
                    
                    if request_id % 50 == 0:
                        logger.info(f"已发送 {request_id} 个请求，当前RPS: {current_rps:.2f}")
                
                # 短暂休眠避免CPU占用过高
                await asyncio.sleep(0.01)
        
        logger.info(f"测试完成，共发送 {request_id} 个请求")
        return results

async def main():
    parser = argparse.ArgumentParser(description="向量模型服务压力测试")
    parser.add_argument("--url", default="http://***********:31027", 
                       help="服务URL")
    parser.add_argument("--max-concurrent", type=int, default=50,
                       help="最大并发数")
    parser.add_argument("--step-size", type=int, default=5,
                       help="并发度递增步长")
    parser.add_argument("--requests-per-step", type=int, default=50,
                       help="每个并发度的请求数")
    parser.add_argument("--output", default=None,
                       help="结果输出文件，不指定则使用时间戳命名")
    parser.add_argument("--test-mode", choices=["gradual", "realistic", "burst", "mixed"],
                       default="gradual", help="测试模式")
    parser.add_argument("--duration", type=int, default=600,
                       help="测试持续时间（秒）")
    parser.add_argument("--base-rps", type=float, default=5.0,
                       help="基础每秒请求数")
    
    args = parser.parse_args()
    
    # 创建测试实例
    tester = EmbeddingStressTest(args.url, args.max_concurrent)
    
    # 检查服务是否可用
    if not await tester.check_service_health():
        logger.error("服务不可用，请检查服务是否正常运行")
        return
    
    logger.info("服务健康检查通过，开始压力测试...")
    
    # 根据测试模式选择测试方法
    if args.test_mode == "gradual":
        # 原有的渐进式负载测试
        results_by_concurrency = await tester.run_gradual_load_test(
            args.max_concurrent, 
            args.step_size, 
            args.requests_per_step
        )
        analysis = tester.analyze_results(results_by_concurrency)
    
    elif args.test_mode == "realistic":
        # 真实负载测试
        results = await tester.run_realistic_load_test(
            duration=args.duration,
            base_rps=args.base_rps
        )
        analysis = tester.analyze_realistic_results(results)
    
    elif args.test_mode == "burst":
        # 突发负载测试
        results = await tester.run_burst_test(
            num_requests=args.requests_per_step * 10,
            burst_duration=args.duration,
            max_concurrent=args.max_concurrent
        )
        analysis = tester.analyze_realistic_results(results)
    
    elif args.test_mode == "mixed":
        # 混合模式测试
        results_by_phase = await tester.run_mixed_pattern_test(
            total_duration=args.duration
        )
        analysis = tester.analyze_mixed_results(results_by_phase)
    
    # 打印结果摘要
    print("\n" + "="*50)
    print("压力测试结果摘要")
    print("="*50)
    
    if analysis.get("summary"):
        summary = analysis["summary"]
        print(f"总请求数: {summary['total_requests']}")
        print(f"成功请求数: {summary['successful_requests']}")
        print(f"成功率: {summary['success_rate']:.2%}")
        print(f"平均响应时间: {summary['avg_response_time']:.3f}s")
        print(f"P95响应时间: {summary['p95_response_time']:.3f}s")
        print(f"P99响应时间: {summary['p99_response_time']:.3f}s")
    
    if analysis.get("recommendations"):
        rec = analysis["recommendations"]
        print(f"\n推荐配置:")
        print(f"最佳并发度: {rec['optimal_concurrency']}")
        print(f"单实例最大吞吐量: {rec['max_throughput_per_instance']:.2f} 请求/秒")
        print(f"2000用户所需实例数: {rec['estimated_instances_for_2000_users']}")
    
    # 保存详细结果
    tester.save_results(analysis, args.output)
    
    print(f"\n详细结果已保存到: {args.output if args.output else '带时间戳的默认文件'}")

if __name__ == "__main__":
    asyncio.run(main())
