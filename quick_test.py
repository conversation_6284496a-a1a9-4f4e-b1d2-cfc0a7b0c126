#!/usr/bin/env python3
"""
快速测试脚本
用于验证服务是否正常工作，进行基本的功能测试
"""

import asyncio
import aiohttp
import argparse
import json
import time
import logging
from typing import Dict, Any

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuickTester:
    """快速测试器"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        
    async def test_health_check(self) -> bool:
        """测试健康检查接口"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/health",
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        logger.info(f"✅ 健康检查通过: {data}")
                        return True
                    else:
                        logger.error(f"❌ 健康检查失败: HTTP {response.status}")
                        return False
        except Exception as e:
            logger.error(f"❌ 健康检查异常: {str(e)}")
            return False

    async def test_embedding_api(self) -> bool:
        """测试向量化接口"""
        test_texts = [
            "这是一个测试文本",
            "人工智能技术正在快速发展",
            "机器学习模型需要大量训练数据"
        ]
        
        try:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "input": test_texts,
                    "model": "bge-m3"
                }
                
                start_time = time.time()
                async with session.post(
                    f"{self.base_url}/embeddings",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    end_time = time.time()
                    response_time = end_time - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        embeddings = data.get("data", [])
                        
                        if len(embeddings) == len(test_texts):
                            logger.info(f"✅ 向量化接口测试通过")
                            logger.info(f"   - 输入文本数: {len(test_texts)}")
                            logger.info(f"   - 输出向量数: {len(embeddings)}")
                            logger.info(f"   - 向量维度: {len(embeddings[0]) if embeddings else 0}")
                            logger.info(f"   - 响应时间: {response_time:.3f}s")
                            return True
                        else:
                            logger.error(f"❌ 向量数量不匹配: 期望{len(test_texts)}, 实际{len(embeddings)}")
                            return False
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ 向量化接口失败: HTTP {response.status} - {error_text}")
                        return False
                        
        except Exception as e:
            logger.error(f"❌ 向量化接口异常: {str(e)}")
            return False

    async def test_stats_api(self) -> bool:
        """测试统计接口"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/stats",
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        logger.info(f"✅ 统计接口测试通过")
                        logger.info(f"   - 总请求数: {data.get('total_requests', 0)}")
                        logger.info(f"   - 当前队列大小: {data.get('current_queue_size', 0)}")
                        logger.info(f"   - 处理中请求: {data.get('processing_requests', 0)}")
                        logger.info(f"   - 平均处理时间: {data.get('average_processing_time', 0):.3f}s")
                        return True
                    else:
                        logger.error(f"❌ 统计接口失败: HTTP {response.status}")
                        return False
        except Exception as e:
            logger.error(f"❌ 统计接口异常: {str(e)}")
            return False

    async def test_config_api(self) -> bool:
        """测试配置接口"""
        try:
            async with aiohttp.ClientSession() as session:
                # 测试配置更新
                config_payload = {
                    "max_queue_size": 150,
                    "max_concurrent_requests": 15
                }
                
                async with session.post(
                    f"{self.base_url}/config",
                    json=config_payload,
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        logger.info(f"✅ 配置接口测试通过")
                        logger.info(f"   - 配置更新: {data.get('config', {})}")
                        return True
                    else:
                        logger.error(f"❌ 配置接口失败: HTTP {response.status}")
                        return False
        except Exception as e:
            logger.error(f"❌ 配置接口异常: {str(e)}")
            return False

    async def test_concurrent_requests(self, num_requests: int = 10) -> bool:
        """测试并发请求"""
        logger.info(f"测试并发请求 ({num_requests} 个请求)...")
        
        async def send_request(session, request_id):
            try:
                payload = {
                    "input": [f"并发测试请求 {request_id}"],
                    "model": "bge-m3"
                }
                
                start_time = time.time()
                async with session.post(
                    f"{self.base_url}/embeddings",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    end_time = time.time()
                    return {
                        "request_id": request_id,
                        "status": response.status,
                        "response_time": end_time - start_time,
                        "success": response.status == 200
                    }
            except Exception as e:
                return {
                    "request_id": request_id,
                    "status": 0,
                    "response_time": 0,
                    "success": False,
                    "error": str(e)
                }
        
        try:
            async with aiohttp.ClientSession() as session:
                tasks = [send_request(session, i) for i in range(num_requests)]
                results = await asyncio.gather(*tasks)
                
                successful = sum(1 for r in results if r["success"])
                success_rate = successful / num_requests
                avg_response_time = sum(r["response_time"] for r in results if r["success"]) / max(1, successful)
                
                if success_rate >= 0.8:  # 80%成功率阈值
                    logger.info(f"✅ 并发测试通过")
                    logger.info(f"   - 成功率: {success_rate:.2%}")
                    logger.info(f"   - 平均响应时间: {avg_response_time:.3f}s")
                    return True
                else:
                    logger.error(f"❌ 并发测试失败: 成功率过低 ({success_rate:.2%})")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ 并发测试异常: {str(e)}")
            return False

    async def test_load_handling(self) -> bool:
        """测试负载处理能力"""
        logger.info("测试负载处理能力...")
        
        # 发送一批请求，测试排队机制
        batch_sizes = [5, 10, 20]
        
        for batch_size in batch_sizes:
            logger.info(f"测试批次大小: {batch_size}")
            
            success = await self.test_concurrent_requests(batch_size)
            if not success:
                logger.error(f"❌ 批次大小 {batch_size} 测试失败")
                return False
            
            # 检查服务状态
            await self.test_stats_api()
            
            # 短暂等待
            await asyncio.sleep(1)
        
        logger.info("✅ 负载处理测试通过")
        return True

    async def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        logger.info("开始快速测试...")
        
        tests = {
            "健康检查": self.test_health_check,
            "向量化接口": self.test_embedding_api,
            "统计接口": self.test_stats_api,
            "配置接口": self.test_config_api,
            "并发请求": lambda: self.test_concurrent_requests(10),
            "负载处理": self.test_load_handling
        }
        
        results = {}
        
        for test_name, test_func in tests.items():
            logger.info(f"\n{'='*50}")
            logger.info(f"运行测试: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                result = await test_func()
                results[test_name] = result
                
                if result:
                    logger.info(f"✅ {test_name} 测试通过")
                else:
                    logger.error(f"❌ {test_name} 测试失败")
                    
            except Exception as e:
                logger.error(f"❌ {test_name} 测试异常: {str(e)}")
                results[test_name] = False
            
            # 测试间隔
            await asyncio.sleep(0.5)
        
        return results

    def print_summary(self, results: Dict[str, bool]):
        """打印测试摘要"""
        logger.info(f"\n{'='*60}")
        logger.info("测试摘要")
        logger.info(f"{'='*60}")
        
        passed = sum(1 for r in results.values() if r)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"{test_name:<20} {status}")
        
        logger.info(f"\n总计: {passed}/{total} 个测试通过")
        
        if passed == total:
            logger.info("🎉 所有测试通过！服务运行正常。")
            return True
        else:
            logger.error("⚠️  部分测试失败，请检查服务配置。")
            return False

async def main():
    parser = argparse.ArgumentParser(description="向量模型服务快速测试")
    parser.add_argument("--url", default="http://localhost:36001", 
                       help="服务URL")
    parser.add_argument("--test", choices=[
        "health", "embedding", "stats", "config", "concurrent", "load", "all"
    ], default="all", help="要运行的测试类型")
    
    args = parser.parse_args()
    
    tester = QuickTester(args.url)
    
    if args.test == "all":
        results = await tester.run_all_tests()
        success = tester.print_summary(results)
        exit(0 if success else 1)
    else:
        # 运行单个测试
        test_map = {
            "health": tester.test_health_check,
            "embedding": tester.test_embedding_api,
            "stats": tester.test_stats_api,
            "config": tester.test_config_api,
            "concurrent": lambda: tester.test_concurrent_requests(10),
            "load": tester.test_load_handling
        }
        
        if args.test in test_map:
            logger.info(f"运行单个测试: {args.test}")
            result = await test_map[args.test]()
            
            if result:
                logger.info(f"✅ {args.test} 测试通过")
                exit(0)
            else:
                logger.error(f"❌ {args.test} 测试失败")
                exit(1)
        else:
            logger.error(f"未知测试类型: {args.test}")
            exit(1)

if __name__ == "__main__":
    asyncio.run(main())
