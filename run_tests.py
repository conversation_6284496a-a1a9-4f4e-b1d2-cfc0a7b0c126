#!/usr/bin/env python3
"""
测试运行脚本
自动化运行各种测试场景，生成完整的性能报告
"""

import asyncio
import subprocess
import json
import time
import argparse
import logging
from pathlib import Path
from typing import Dict, Any, List
import matplotlib.pyplot as plt
import pandas as pd

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestRunner:
    """测试运行器"""
    
    def __init__(self, service_url: str = "http://localhost:36001"):
        self.service_url = service_url
        self.results_dir = Path("test_results")
        self.results_dir.mkdir(exist_ok=True)
        
    async def run_stress_test(self, max_concurrent: int = 50) -> Dict[str, Any]:
        """运行压力测试"""
        logger.info("开始运行压力测试...")
        
        output_file = self.results_dir / f"stress_test_{int(time.time())}.json"
        
        cmd = [
            "python", "embedding_stress_test.py",
            "--url", self.service_url,
            "--max-concurrent", str(max_concurrent),
            "--step-size", "5",
            "--requests-per-step", "50",
            "--output", str(output_file)
        ]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                logger.info("压力测试完成")
                with open(output_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                logger.error(f"压力测试失败: {stderr.decode()}")
                return {}
                
        except Exception as e:
            logger.error(f"运行压力测试时出错: {str(e)}")
            return {}

    async def run_client_simulation(self, num_users: int = 100, 
                                  duration: int = 300,
                                  arrival_pattern: str = "uniform") -> Dict[str, Any]:
        """运行客户端模拟测试"""
        logger.info(f"开始运行客户端模拟测试: {num_users} 用户, {duration}s")
        
        output_file = self.results_dir / f"client_sim_{arrival_pattern}_{int(time.time())}.json"
        
        cmd = [
            "python", "client_simulation.py",
            "--url", self.service_url,
            "--users", str(num_users),
            "--duration", str(duration),
            "--arrival-pattern", arrival_pattern,
            "--output", str(output_file)
        ]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                logger.info("客户端模拟测试完成")
                with open(output_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                logger.error(f"客户端模拟测试失败: {stderr.decode()}")
                return {}
                
        except Exception as e:
            logger.error(f"运行客户端模拟测试时出错: {str(e)}")
            return {}

    def generate_comprehensive_report(self, stress_results: Dict[str, Any],
                                    simulation_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成综合测试报告"""
        report = {
            "test_timestamp": time.time(),
            "service_url": self.service_url,
            "stress_test": stress_results,
            "client_simulations": simulation_results,
            "capacity_planning": {},
            "recommendations": []
        }
        
        # 容量规划分析
        if stress_results.get("recommendations"):
            stress_rec = stress_results["recommendations"]
            
            # 基于压力测试结果
            max_throughput = stress_rec.get("max_throughput_per_instance", 0)
            optimal_concurrency = stress_rec.get("optimal_concurrency", 1)
            
            # 基于客户端模拟结果
            sim_analysis = []
            for sim in simulation_results:
                if sim.get("analysis"):
                    sim_analysis.append(sim["analysis"])
            
            # 计算不同负载下的性能
            capacity_planning = {
                "single_instance_capacity": {
                    "max_throughput_rps": max_throughput,
                    "optimal_concurrency": optimal_concurrency,
                    "recommended_queue_size": 100
                },
                "scaling_recommendations": self._calculate_scaling_recommendations(
                    max_throughput, sim_analysis
                ),
                "performance_thresholds": {
                    "response_time_p95_threshold": 2.0,  # 秒
                    "success_rate_threshold": 0.95,
                    "user_satisfaction_threshold": 0.8
                }
            }
            
            report["capacity_planning"] = capacity_planning
        
        # 生成建议
        recommendations = self._generate_comprehensive_recommendations(
            stress_results, simulation_results
        )
        report["recommendations"] = recommendations
        
        return report

    def _calculate_scaling_recommendations(self, max_throughput: float, 
                                         sim_analysis: List[Dict]) -> Dict[str, Any]:
        """计算扩缩容建议"""
        scaling = {
            "for_2000_users": {},
            "for_5000_users": {},
            "for_10000_users": {},
            "auto_scaling_rules": []
        }
        
        # 假设每用户每分钟1个请求的基准负载
        base_rps_per_user = 1 / 60  # 每秒请求数
        
        for user_count in [2000, 5000, 10000]:
            required_rps = user_count * base_rps_per_user
            required_instances = max(1, int(required_rps / max_throughput) + 1)
            
            # 添加安全边际
            recommended_instances = int(required_instances * 1.3)
            
            scaling[f"for_{user_count}_users"] = {
                "required_rps": required_rps,
                "min_instances": required_instances,
                "recommended_instances": recommended_instances,
                "safety_margin": "30%"
            }
        
        # 自动扩缩容规则
        scaling["auto_scaling_rules"] = [
            {
                "metric": "queue_size",
                "scale_up_threshold": 50,
                "scale_down_threshold": 10,
                "action": "基于队列长度扩缩容"
            },
            {
                "metric": "response_time_p95",
                "scale_up_threshold": 2.0,
                "scale_down_threshold": 0.5,
                "action": "基于响应时间扩缩容"
            },
            {
                "metric": "cpu_utilization",
                "scale_up_threshold": 70,
                "scale_down_threshold": 30,
                "action": "基于CPU使用率扩缩容"
            }
        ]
        
        return scaling

    def _generate_comprehensive_recommendations(self, stress_results: Dict[str, Any],
                                              simulation_results: List[Dict[str, Any]]) -> List[str]:
        """生成综合建议"""
        recommendations = []
        
        # 基于压力测试的建议
        if stress_results.get("summary"):
            summary = stress_results["summary"]
            if summary.get("success_rate", 0) < 0.95:
                recommendations.append("压力测试显示成功率偏低，建议优化服务性能或增加实例数")
            
            if summary.get("p95_response_time", 0) > 2.0:
                recommendations.append("P95响应时间超过2秒，建议优化算法或增加计算资源")
        
        # 基于客户端模拟的建议
        for sim in simulation_results:
            if sim.get("analysis"):
                analysis = sim["analysis"]
                if analysis.get("user_satisfaction_score", 0) < 0.8:
                    recommendations.append("用户满意度偏低，建议改善服务质量")
                
                if analysis.get("users_with_zero_success", 0) > 0:
                    recommendations.append("存在完全无法获得服务的用户，建议增加服务容量")
        
        # 通用建议
        recommendations.extend([
            "建议实施监控告警系统，实时监控服务性能指标",
            "建议设置自动扩缩容策略，应对流量波动",
            "建议定期进行压力测试，验证服务容量",
            "建议实施熔断机制，防止服务雪崩",
            "建议优化模型推理性能，减少单次请求处理时间"
        ])
        
        return recommendations

    def create_performance_charts(self, report: Dict[str, Any]):
        """创建性能图表"""
        try:
            import matplotlib.pyplot as plt
            import matplotlib
            matplotlib.use('Agg')  # 使用非交互式后端
            
            # 创建图表目录
            charts_dir = self.results_dir / "charts"
            charts_dir.mkdir(exist_ok=True)
            
            # 压力测试结果图表
            if report.get("stress_test", {}).get("by_concurrency"):
                self._create_stress_test_chart(
                    report["stress_test"]["by_concurrency"],
                    charts_dir / "stress_test_performance.png"
                )
            
            # 客户端模拟结果图表
            if report.get("client_simulations"):
                self._create_simulation_chart(
                    report["client_simulations"],
                    charts_dir / "client_simulation_results.png"
                )
            
            logger.info(f"性能图表已保存到 {charts_dir}")
            
        except ImportError:
            logger.warning("matplotlib未安装，跳过图表生成")
        except Exception as e:
            logger.error(f"生成图表时出错: {str(e)}")

    def _create_stress_test_chart(self, by_concurrency: Dict, output_path: Path):
        """创建压力测试图表"""
        concurrency_levels = sorted(by_concurrency.keys())
        success_rates = [by_concurrency[c]["success_rate"] for c in concurrency_levels]
        response_times = [by_concurrency[c]["avg_response_time"] for c in concurrency_levels]
        throughputs = [by_concurrency[c]["throughput"] for c in concurrency_levels]
        
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(10, 12))
        
        # 成功率
        ax1.plot(concurrency_levels, success_rates, 'b-o')
        ax1.set_xlabel('并发度')
        ax1.set_ylabel('成功率')
        ax1.set_title('成功率 vs 并发度')
        ax1.grid(True)
        
        # 响应时间
        ax2.plot(concurrency_levels, response_times, 'r-o')
        ax2.set_xlabel('并发度')
        ax2.set_ylabel('平均响应时间 (秒)')
        ax2.set_title('响应时间 vs 并发度')
        ax2.grid(True)
        
        # 吞吐量
        ax3.plot(concurrency_levels, throughputs, 'g-o')
        ax3.set_xlabel('并发度')
        ax3.set_ylabel('吞吐量 (请求/秒)')
        ax3.set_title('吞吐量 vs 并发度')
        ax3.grid(True)
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

    def _create_simulation_chart(self, simulations: List[Dict], output_path: Path):
        """创建客户端模拟图表"""
        if not simulations:
            return
            
        patterns = []
        success_rates = []
        satisfaction_scores = []
        avg_response_times = []
        
        for sim in simulations:
            if sim.get("analysis"):
                analysis = sim["analysis"]
                # 从文件名或其他地方获取模式信息，这里简化处理
                patterns.append("simulation")
                success_rates.append(analysis.get("success_rate", 0))
                satisfaction_scores.append(analysis.get("user_satisfaction_score", 0))
                avg_response_times.append(analysis.get("average_response_time", 0))
        
        if not patterns:
            return
            
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))
        
        x = range(len(patterns))
        
        # 成功率和满意度
        ax1.bar([i - 0.2 for i in x], success_rates, 0.4, label='成功率', alpha=0.7)
        ax1.bar([i + 0.2 for i in x], satisfaction_scores, 0.4, label='用户满意度', alpha=0.7)
        ax1.set_xlabel('测试场景')
        ax1.set_ylabel('评分')
        ax1.set_title('客户端模拟测试结果')
        ax1.set_xticks(x)
        ax1.set_xticklabels(patterns)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 响应时间
        ax2.bar(x, avg_response_times, alpha=0.7, color='orange')
        ax2.set_xlabel('测试场景')
        ax2.set_ylabel('平均响应时间 (秒)')
        ax2.set_title('平均响应时间')
        ax2.set_xticks(x)
        ax2.set_xticklabels(patterns)
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

    def save_report(self, report: Dict[str, Any], filename: str = None):
        """保存测试报告"""
        if filename is None:
            filename = f"comprehensive_test_report_{int(time.time())}.json"
        
        output_path = self.results_dir / filename
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"综合测试报告已保存到 {output_path}")
        return output_path

async def main():
    parser = argparse.ArgumentParser(description="运行完整的性能测试套件")
    parser.add_argument("--url", default="http://localhost:36001", 
                       help="服务URL")
    parser.add_argument("--max-concurrent", type=int, default=50,
                       help="压力测试最大并发数")
    parser.add_argument("--sim-users", type=int, default=100,
                       help="客户端模拟用户数")
    parser.add_argument("--sim-duration", type=int, default=300,
                       help="客户端模拟持续时间")
    parser.add_argument("--skip-stress", action="store_true",
                       help="跳过压力测试")
    parser.add_argument("--skip-simulation", action="store_true",
                       help="跳过客户端模拟")
    
    args = parser.parse_args()
    
    runner = TestRunner(args.url)
    
    print("="*60)
    print("向量模型服务性能测试套件")
    print("="*60)
    
    stress_results = {}
    simulation_results = []
    
    # 运行压力测试
    if not args.skip_stress:
        print("\n1. 运行压力测试...")
        stress_results = await runner.run_stress_test(args.max_concurrent)
    
    # 运行客户端模拟测试
    if not args.skip_simulation:
        print("\n2. 运行客户端模拟测试...")
        
        # 测试不同的到达模式
        for pattern in ["uniform", "burst", "gradual"]:
            print(f"   测试 {pattern} 模式...")
            sim_result = await runner.run_client_simulation(
                args.sim_users, args.sim_duration, pattern
            )
            if sim_result:
                simulation_results.append(sim_result)
    
    # 生成综合报告
    print("\n3. 生成综合报告...")
    report = runner.generate_comprehensive_report(stress_results, simulation_results)
    
    # 创建图表
    print("4. 生成性能图表...")
    runner.create_performance_charts(report)
    
    # 保存报告
    report_path = runner.save_report(report)
    
    # 打印摘要
    print("\n" + "="*60)
    print("测试完成摘要")
    print("="*60)
    
    if report.get("capacity_planning", {}).get("scaling_recommendations"):
        scaling = report["capacity_planning"]["scaling_recommendations"]
        print(f"2000用户推荐实例数: {scaling.get('for_2000_users', {}).get('recommended_instances', 'N/A')}")
        print(f"5000用户推荐实例数: {scaling.get('for_5000_users', {}).get('recommended_instances', 'N/A')}")
        print(f"10000用户推荐实例数: {scaling.get('for_10000_users', {}).get('recommended_instances', 'N/A')}")
    
    if report.get("recommendations"):
        print(f"\n关键建议:")
        for i, rec in enumerate(report["recommendations"][:5], 1):
            print(f"{i}. {rec}")
    
    print(f"\n详细报告: {report_path}")
    print(f"测试结果目录: {runner.results_dir}")

if __name__ == "__main__":
    asyncio.run(main())
