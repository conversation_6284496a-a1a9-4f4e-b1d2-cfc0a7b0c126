import axios from 'axios';
import { EMBED_MODEL_URL } from './constant';

export class ZeroAgentEmbedding {
    private url: string;
    private retryDelay: number;
    private maxRetries: number;
    private concurrency: number;
    private requestTimeout: number;
    private concurrencyInterval: number;

    constructor(url: string = EMBED_MODEL_URL) {
        this.url = url;
        this.maxRetries = 2;
        this.concurrency = 1;
        this.retryDelay = 100;              /* 100 ms */
        this.requestTimeout = 10000;        /* 10 s */
        this.concurrencyInterval = 100;     /* 100 ms */
    }

    public getEmbeddingConcurrency(): number {
        return this.concurrency;
    }

    private getRetryDelay(attempt: number): number {
        /* 指数退避 + 随机抖动（Jitter）+ 最大退避上限 */
        return Math.floor(Math.min((Math.pow(2, attempt - 1) * this.retryDelay * (0.5 + Math.random())), 1000))
    }

    async requestEmbeddingServer(texts: string[]): Promise<number[][]> {
        try {
            const response = await axios.post(this.url, {
                input: texts
            }, {
                headers: {
                    'User-Agent': 'Test Client'
                },
                timeout: this.requestTimeout,
            });

            if (!Array.isArray(response.data?.data) || !response.data.data.length) {
                throw new Error(`The data in the response must be a valid`);
            }

            return response.data.data;
        } catch (error) {
            if (axios.isAxiosError(error)) {
                /* HTTP 错误（4xx/5xx） */
                if (error.response) {
                    throw new Error(`HTTP request failed with status code: ${error.response.status} - ${error.response.data?.message || 'No error details'}`);
                }
                /* 网络错误（无响应） */
                else if (error.request) {
                    throw new Error(`The request is not responding, the network is wrong`);
                }
                /* 请求配置错误 */
                else {
                    throw new Error(`Request Misconfiguration: ${error.message}`);
                }
            }
            /* 其他错误（如业务逻辑错误） */
            else {
                throw error instanceof Error ? error : new Error(`Unknown error`);
            }
        }
    }

    async requestEmbeddingServerWithRetries(texts: string[]): Promise<number[][]> {
        let attempt: number = 0;

        while (attempt++ < this.maxRetries) {
            try {
                return await this.requestEmbeddingServer(texts);
            } catch (error) {
                if (attempt <= this.maxRetries) {
                    console.warn(`Request embedding server fail: ${error.message}`);

                    const delay = this.getRetryDelay(attempt);
                    await new Promise(resolve => setTimeout(resolve, delay))
                        .then(() => console.log(`Retry request (${attempt}/${this.maxRetries} times, delay ${delay} ms`))
                        .catch(error => console.error(`The retry request delay ${delay} ms failed: ${error.message}`));
                }
                else {
                    /* 一定要主动异常，上层需要捕获异常 */
                    throw error;
                }
            }
        }
        return [];
    }

    async getBatchEmbeddings(texts: string[][]): Promise<number[][][]> {
        let currentIndex: number = 0;
        const results: number[][][] = new Array(texts.length);

        const runBatch = async (): Promise<void> => {
            const batchPromises: Promise<void>[] = [];
            const batchSize = Math.min(this.concurrency, (texts.length - currentIndex));

            for (let i = 0; i < batchSize; i++) {
                const index = currentIndex++;
                const text = texts[index];
                const task = new Promise<void>(async (resolve) => {
                    /* 按间隔启动任务 */
                    setTimeout(async () => {
                        try {
                            results[index] = await this.requestEmbeddingServerWithRetries(text);
                        } catch (error) {
                            /* 请求失败需要存一个空数组，保证顺序 */
                            results[index] = [];
                            console.error(`Request embedding server fail: ${error.message}`);
                        } finally {
                            resolve();
                        }
                    }, (i * this.concurrencyInterval));
                });
                batchPromises.push(task);
            }

            // QUES 要不要使用 allSettled ？
            await Promise.all(batchPromises);
        };

        /* 分批执行所有任务 */
        while (currentIndex < texts.length) {
            await runBatch();
        }

        return results;
    }

    async getEmbeddings(texts: string[]): Promise<number[][]> {
        try {
            return await this.requestEmbeddingServerWithRetries(texts);
        } catch (error) {
            console.error(`Request embedding server fail: ${error.message}`);
            return [];
        }
    }

    async getGeneralTextEmbedding(text: any) {
        try {
            const response = await axios.post(this.url, {
                input: text
            }, {
                headers: {
                    'User-Agent': 'Test Client'
                },
                timeout: 10000 // 10 seconds timeout
            });

            return response.data.data;
        } catch (error) {
            console.error('Error fetching embedding:', error);
            throw error;
        }
    }

    async getTextEmbedding(text: string): Promise<number[]> {
        return this.getGeneralTextEmbedding(text);
    }

    async embed(texts: string[]): Promise<number[][]> {
        return this.getGeneralTextEmbedding(texts);
    }
}