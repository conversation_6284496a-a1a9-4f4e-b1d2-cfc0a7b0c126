# 向量模型服务吞吐测试步骤

## 环境准备

### 1. 安装依赖
```bash
# 安装Python依赖
make install
# 或者
pip install -r requirements.txt
```

### 2. 检查环境
确保您有以下环境：
- Python 3.8+
- 足够的内存运行向量模型（建议8GB+）
- 可用的GPU（可选，但推荐）

## 测试流程

### 第一步：启动服务

#### 方式1：使用Makefile（推荐）
```bash
# 后台启动服务
make start-daemon

# 查看服务状态
make status
```

#### 方式2：使用启动脚本
```bash
# 后台启动
./start_service.sh --daemon

# 前台启动（用于调试）
./start_service.sh

# 查看状态
./start_service.sh --status
```

#### 方式3：直接启动
```bash
cd embedding
python embedding_server.py --host 0.0.0.0 --port 36001 --embedding-model BAAI/bge-large-zh
```

### 第二步：验证服务

#### 快速功能测试
```bash
# 运行所有快速测试
make quick-test

# 或者运行特定测试
python quick_test.py --test health      # 健康检查
python quick_test.py --test embedding   # 向量化功能
python quick_test.py --test concurrent  # 并发测试
```

#### 手动验证
```bash
# 健康检查
curl http://localhost:36001/health

# 测试向量化
curl -X POST http://localhost:36001/embeddings \
  -H "Content-Type: application/json" \
  -d '{"input": ["测试文本"], "model": "bge-m3"}'

# 查看统计信息
curl http://localhost:36001/stats
```

### 第三步：压力测试

#### 运行压力测试
```bash
# 使用默认参数
make stress-test

# 或者自定义参数
python embedding_stress_test.py \
  --url http://localhost:36001 \
  --max-concurrent 50 \
  --step-size 5 \
  --requests-per-step 50
```

#### 高负载测试
```bash
# 测试更高的并发度
make test-high-load

# 或者
python embedding_stress_test.py --max-concurrent 100
```

### 第四步：客户端模拟

#### 运行不同模式的客户端模拟
```bash
# 均匀到达模式
python client_simulation.py --arrival-pattern uniform --users 100

# 突发到达模式
make test-burst

# 渐进到达模式
make test-gradual
```

#### 大规模用户模拟
```bash
# 模拟2000用户（分批测试）
python client_simulation.py --users 500 --duration 600
python client_simulation.py --users 1000 --duration 600
python client_simulation.py --users 2000 --duration 600
```

### 第五步：队列大小优化测试

#### 运行队列优化测试
```bash
# 使用默认测试范围
make queue-opt

# 或自定义测试范围
python queue_size_optimizer.py \
  --queue-sizes 25 50 100 150 200 300 \
  --test-requests 100 \
  --concurrent-level 50
```

#### 分析队列配置结果
测试会输出：
- 不同队列大小的性能对比
- 最优队列大小推荐
- 用户满意度评分
- 拒绝率和响应时间分析

### 第六步：完整测试套件

#### 运行完整测试
```bash
# 运行所有测试并生成报告
make full-test

# 或者
python run_tests.py \
  --max-concurrent 50 \
  --sim-users 100 \
  --sim-duration 300
```

#### 自定义测试场景
```bash
# 跳过压力测试，只做客户端模拟
python run_tests.py --skip-stress

# 跳过客户端模拟，只做压力测试
python run_tests.py --skip-simulation
```

## 结果分析

### 查看测试结果
```bash
# 查看最新测试报告
make report

# 查看详细结果文件
ls test_results/

# 查看图表（如果生成）
ls test_results/charts/
```

### 关键指标解读

#### 压力测试指标
- **最佳并发度**: 在保证高成功率下的最大并发数
- **最大吞吐量**: 每秒处理的最大请求数
- **P95响应时间**: 95%的请求在此时间内完成
- **成功率**: 成功处理的请求比例

#### 客户端模拟指标
- **用户满意度**: 综合成功率和响应时间的评分
- **零成功用户数**: 完全无法获得服务的用户数量
- **平均响应时间**: 所有成功请求的平均处理时间

### 容量规划建议

根据测试结果，系统会自动计算：

1. **单实例容量**
   - 最大承载用户数
   - 推荐并发配置
   - 队列大小设置

2. **多实例规划**
   - 2000用户所需实例数
   - 5000用户所需实例数
   - 10000用户所需实例数

3. **扩缩容策略**
   - 基于队列长度的扩容规则
   - 基于响应时间的扩容规则
   - 基于CPU使用率的扩容规则

## 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查端口占用
netstat -tuln | grep 36001

# 检查依赖
python -c "import fastapi, uvicorn, sentence_transformers"

# 查看详细错误
./start_service.sh  # 前台启动查看错误
```

#### 2. 测试连接失败
```bash
# 检查服务状态
make status

# 检查防火墙
curl -v http://localhost:36001/health

# 检查服务日志
make logs
```

#### 3. 性能异常
```bash
# 检查系统资源
top
nvidia-smi  # 如果使用GPU

# 检查服务统计
make stats

# 调整服务配置
curl -X POST http://localhost:36001/config \
  -H "Content-Type: application/json" \
  -d '{"max_queue_size": 200, "max_concurrent_requests": 20}'
```

### 性能优化建议

#### 服务端优化
1. **调整并发参数**
   ```bash
   # 增加最大并发处理数
   curl -X POST http://localhost:36001/config \
     -d '{"max_concurrent_requests": 20}'
   ```

2. **调整队列大小**
   ```bash
   # 增加队列容量
   curl -X POST http://localhost:36001/config \
     -d '{"max_queue_size": 200}'
   ```

3. **模型优化**
   - 使用更小的模型（如果精度允许）
   - 启用模型量化
   - 使用GPU加速

#### 系统优化
1. **硬件配置**
   - 增加内存
   - 使用SSD存储
   - 使用GPU加速

2. **网络优化**
   - 增加网络带宽
   - 优化网络延迟
   - 使用负载均衡

## 持续监控

### 生产环境监控
```bash
# 定期健康检查
*/5 * * * * curl -s http://localhost:36001/health

# 性能监控
*/1 * * * * curl -s http://localhost:36001/stats >> /var/log/embedding_stats.log

# 定期压力测试
0 2 * * 0 cd /path/to/test && python embedding_stress_test.py --max-concurrent 30
```

### 告警设置
建议设置以下告警：
- 响应时间超过2秒
- 成功率低于95%
- 队列长度超过阈值
- CPU/内存使用率过高

## 清理

### 停止服务和清理
```bash
# 停止服务
make stop

# 清理测试结果
make clean

# 完全清理
rm -rf test_results/
rm -f *.json
```

## 总结

通过以上测试步骤，您可以：

1. ✅ 验证服务基本功能
2. ✅ 测试服务承载能力
3. ✅ 模拟真实用户场景
4. ✅ 获得容量规划建议
5. ✅ 估算2000用户所需实例数

测试完成后，您将获得详细的性能报告和容量规划建议，帮助您在生产环境中正确配置和部署向量模型服务。
