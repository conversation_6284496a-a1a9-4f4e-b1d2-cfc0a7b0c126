import argparse
import json
from typing import Union
import logging
from logging.handlers import TimedRotatingFileHandler

from pydantic import BaseModel, Field
import uvicorn
from fastapi import FastAPI
from fastapi.responses import JSONResponse, Response
import torch

from src.infrastructure.tools.embedding import HuggingFaceEmbedding

app = FastAPI()


TIMEOUT_KEEP_ALIVE = 5  # seconds.

log_handler = TimedRotatingFileHandler(
    "logs/logfile",
    when="midnight",
    interval=1,
    backupCount=0
)
log_handler.suffix = "%Y-%m-%d.log"
log_handler.setFormatter(logging.Formatter(
    "%(asctime)s %(pathname)s %(filename)s %(lineno)s [%(levelname)s] %(message)s"))

logging.basicConfig(
    level=logging.INFO,
    handlers=[log_handler],
)

# Started by AICoder, pid:s46fc09f3bc57b01462a097c80e94a16f7683c2a
def parse_model_kwargs(arg_str):
    """
    Parse model keyword arguments from a string.
    Handles special cases like torch.float16, torch.float32, etc.
    """
    try:
        # First try to parse as JSON
        kwargs = json.loads(arg_str)

        # Process special string values that JSON can't handle directly
        for key, value in kwargs.items():
            if isinstance(value, str):
                # Handle torch dtype values
                if value == "torch.float16":
                    kwargs[key] = torch.float16
                elif value == "torch.float32":
                    kwargs[key] = torch.float32
                elif value == "torch.bfloat16":
                    kwargs[key] = torch.bfloat16
                elif value == "torch.int8":
                    kwargs[key] = torch.int8
                # Add more special cases as needed

        return kwargs
    except json.JSONDecodeError:
        # If JSON parsing fails, fall back to eval (less safe)
        return eval(arg_str)

def create_parser():
    parser = argparse.ArgumentParser(description="Service Configuration")
    parser.add_argument("--host", type=str,
                        default="0.0.0.0", help="Host IP address")
    parser.add_argument("--port", type=int, default=36001, help="Port number")
    parser.add_argument(
        "--embedding-model",
        type=str,
        default='BAAI/bge-large-zh',
        help='Path of the Hugging Face embedding model',
    )
    parser.add_argument("--batch", type=int, default=1, help="Batch size")
    parser.add_argument("--trust-remote-code", action= "store_true", default=False, help="trust remote code")
    parser.add_argument(
        "--model-kwargs",
        type=parse_model_kwargs,
        default={},
        help='Additional keyword arguments for the model as a dictionary, e.g., \'{"device": "cuda", "torch_dtype": "torch.float16"}\'',
    )
    return parser
# Ended by AICoder, pid:s46fc09f3bc57b01462a097c80e94a16f7683c2a


class GetEmbeddings(BaseModel):
    input: Union[str, list] = Field(title='Embedding Strings', default=[])
    model: str = Field(title='Embedding Model', default="bge-m3")


@app.post("/embeddings")
async def get_embeddings(request: GetEmbeddings) -> Response:
    logging.info(f"input: {request.input[:50]}...")
    response = hf_embed.get_text_embedding(request.input, request.model)
    return JSONResponse(response)


if __name__ == "__main__":
    parser = create_parser()
    args = parser.parse_args()
    print(f"embed mode: {args.embedding_model}")
    print(f"trust remote code: {args.trust_remote_code}")
    hf_embed = HuggingFaceEmbedding(
        model_dir=args.embedding_model, trust_remote_code=args.trust_remote_code, model_kwargs=args.model_kwargs, embed_batch_size=args.batch)
    uvicorn.run(
        app,
        host=args.host,
        port=args.port,
        log_level="debug",
        timeout_keep_alive=TIMEOUT_KEEP_ALIVE,
    )