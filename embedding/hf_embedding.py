from typing import Optional, List

from sentence_transformers import SentenceTransformer

from src.infrastructure.tools.remote_server_util import infer_torch_device


class HuggingFaceEmbedding:
    def __init__(
            self,
            model_dir: str,
            embed_batch_size: int = 1,
            normalize: bool = True,
            device: Optional[str] = None,
            trust_remote_code: bool = False,
            **model_kwargs,
    ):
        device = device or infer_torch_device()
        model = SentenceTransformer(
            model_dir,
            device=device,
            trust_remote_code=trust_remote_code,
            **model_kwargs,
        )
        self._model = model
        self.embed_batch_size = embed_batch_size
        self.normalize = normalize

    def get_text_embedding(self, sentences: List[str], embed_model: str):
        emb = self._model.encode(
            sentences,
            batch_size=self.embed_batch_size,
            normalize_embeddings=self.normalize,
        )
        embed_result = {"data": emb.tolist(), "model": embed_model}
        return embed_result
