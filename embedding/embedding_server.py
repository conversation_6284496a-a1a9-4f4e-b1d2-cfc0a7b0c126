import argparse
import asyncio
import time
import logging
from typing import Union
from collections import deque
import threading

from pydantic import BaseModel, Field
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse, Response
from fastapi.middleware.cors import CORSMiddleware

from hf_embedding import HuggingFaceEmbedding

app = FastAPI()

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

TIMEOUT_KEEP_ALIVE = 5  # seconds.

# 全局变量用于性能监控和排队
class PerformanceMonitor:
    def __init__(self):
        self.request_count = 0
        self.total_processing_time = 0.0
        self.queue_size = 0
        self.max_queue_size = 100  # 最大队列长度
        self.request_queue = deque()
        self.processing_requests = 0
        self.max_concurrent_requests = 10  # 最大并发处理数
        self.lock = threading.Lock()

    def add_request(self, request_id: str):
        with self.lock:
            if len(self.request_queue) >= self.max_queue_size:
                raise HTTPException(status_code=503, detail="服务器繁忙，请稍后重试")
            self.request_queue.append({
                'id': request_id,
                'timestamp': time.time()
            })
            self.queue_size = len(self.request_queue)

    def get_next_request(self):
        with self.lock:
            if self.request_queue and self.processing_requests < self.max_concurrent_requests:
                self.processing_requests += 1
                return self.request_queue.popleft()
            return None

    def finish_request(self, processing_time: float):
        with self.lock:
            self.processing_requests -= 1
            self.request_count += 1
            self.total_processing_time += processing_time
            self.queue_size = len(self.request_queue)

    def get_stats(self):
        with self.lock:
            avg_time = self.total_processing_time / max(1, self.request_count)
            return {
                "total_requests": self.request_count,
                "average_processing_time": avg_time,
                "current_queue_size": self.queue_size,
                "processing_requests": self.processing_requests,
                "max_queue_size": self.max_queue_size,
                "max_concurrent_requests": self.max_concurrent_requests
            }

monitor = PerformanceMonitor()


# Started by AICoder, pid:s46fc09f3bc57b01462a097c80e94a16f7683c2a
def create_parser():
    parser = argparse.ArgumentParser(description="Service Configuration")
    parser.add_argument("--host", type=str,
                        default="0.0.0.0", help="Host IP address")
    parser.add_argument("--port", type=int, default=36001, help="Port number")
    parser.add_argument(
        "--embedding-model",
        type=str,
        default='BAAI/bge-large-zh',
        help='Path of the Hugging Face embedding model',
    )
    return parser
# Ended by AICoder, pid:s46fc09f3bc57b01462a097c80e94a16f7683c2a


class GetEmbeddings(BaseModel):
    input: Union[str, list] = Field(title='Embedding Strings', default=[])
    model: str = Field(title='Embedding Model', default="bge-m3")


@app.post("/embeddings")
async def get_embeddings(request: GetEmbeddings) -> Response:
    import uuid
    request_id = str(uuid.uuid4())

    # 添加到队列
    monitor.add_request(request_id)

    # 等待处理机会
    while True:
        queued_request = monitor.get_next_request()
        if queued_request and queued_request['id'] == request_id:
            break
        await asyncio.sleep(0.01)  # 短暂等待

    start_time = time.time()
    try:
        response = hf_embed.get_text_embedding(request.input, request.model)
        processing_time = time.time() - start_time
        monitor.finish_request(processing_time)
        return JSONResponse(response)
    except Exception as e:
        processing_time = time.time() - start_time
        monitor.finish_request(processing_time)
        raise HTTPException(status_code=500, detail=f"处理请求时出错: {str(e)}")

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "timestamp": time.time()}

@app.get("/stats")
async def get_stats():
    """获取性能统计信息"""
    return monitor.get_stats()

@app.post("/config")
async def update_config(max_queue_size: int = None, max_concurrent_requests: int = None):
    """更新服务配置"""
    if max_queue_size is not None:
        monitor.max_queue_size = max_queue_size
    if max_concurrent_requests is not None:
        monitor.max_concurrent_requests = max_concurrent_requests
    return {"message": "配置已更新", "config": {
        "max_queue_size": monitor.max_queue_size,
        "max_concurrent_requests": monitor.max_concurrent_requests
    }}


if __name__ == "__main__":
    parser = create_parser()
    args = parser.parse_args()

    hf_embed = HuggingFaceEmbedding(
        model_dir=args.embedding_model, embed_batch_size=1)
    uvicorn.run(
        app,
        host=args.host,
        port=args.port,
        log_level="debug",
        timeout_keep_alive=TIMEOUT_KEEP_ALIVE,
    )
