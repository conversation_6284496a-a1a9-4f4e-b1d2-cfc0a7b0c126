#!/usr/bin/env python3
"""
快速测试脚本 - 验证吞吐量测试功能
用于快速验证代码修改是否正确
"""

import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from embedding_stress_test import EmbeddingStressTest

async def quick_test():
    """快速测试新的吞吐量测试功能"""
    print("开始快速测试...")
    
    # 创建测试实例
    tester = EmbeddingStressTest("http://10.55.26.91:31027", 10)
    
    # 测试数据生成
    print(f"生成的测试数据数量: {len(tester.test_data)}")
    if len(tester.test_data) > 0:
        print(f"示例代码片段长度: {len(tester.test_data[0])} 字符")
    
    # 测试服务健康检查
    print("检查服务健康状态...")
    try:
        health = await tester.check_service_health()
        print(f"服务健康状态: {'正常' if health else '异常'}")
    except Exception as e:
        print(f"健康检查异常: {e}")
    
    # 测试单个请求（如果服务可用）
    if await tester.check_service_health():
        print("测试单个请求...")
        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                result = await tester.send_single_request_with_timeout(
                    session, 
                    tester.test_data[0], 
                    "test_001", 
                    10.0
                )
                print(f"单个请求结果: 成功={result.success}, 响应时间={result.response_time:.3f}s")
        except Exception as e:
            print(f"单个请求测试异常: {e}")
    
    # 测试分析功能
    print("测试结果分析功能...")
    from embedding_stress_test import TestResult
    import time
    
    # 创建模拟测试结果
    mock_results = {
        5: [
            TestResult("req_001", time.time(), time.time() + 1.0, 1.0, True),
            TestResult("req_002", time.time(), time.time() + 1.5, 1.5, True),
            TestResult("req_003", time.time(), time.time() + 2.0, 2.0, True),
        ],
        10: [
            TestResult("req_004", time.time(), time.time() + 2.0, 2.0, True),
            TestResult("req_005", time.time(), time.time() + 3.0, 3.0, True),
            TestResult("req_006", time.time(), time.time() + 10.0, 10.0, False, "Request timeout"),
        ]
    }
    
    analysis = tester.analyze_throughput_results(mock_results)
    print(f"分析结果包含的键: {list(analysis.keys())}")
    print(f"最大可持续RPS: {analysis.get('max_sustainable_rps', 'N/A')}")
    
    if analysis.get("recommendations"):
        rec = analysis["recommendations"]
        print(f"推荐安全RPS: {rec.get('recommended_safe_rps', 'N/A')}")
        print(f"建议队列大小: {rec.get('queue_size_recommendations', {}).get('max_queue_size', 'N/A')}")
    
    print("快速测试完成！")

if __name__ == "__main__":
    asyncio.run(quick_test())
