{"service": {"host": "0.0.0.0", "port": 36001, "embedding_model": "BAAI/bge-large-zh", "max_queue_size": 100, "max_concurrent_requests": 10, "timeout_keep_alive": 5}, "testing": {"stress_test": {"max_concurrent": 50, "step_size": 5, "requests_per_step": 50, "timeout": 30}, "client_simulation": {"default_users": 100, "default_duration": 300, "arrival_patterns": ["uniform", "burst", "gradual"], "request_interval_range": [5, 30]}, "capacity_planning": {"target_users": [2000, 5000, 10000], "requests_per_user_per_minute": 1, "safety_margin": 0.3, "performance_thresholds": {"response_time_p95": 2.0, "success_rate": 0.95, "user_satisfaction": 0.8}}}, "monitoring": {"stats_interval": 10, "log_level": "INFO", "enable_charts": true, "results_retention_days": 30}}