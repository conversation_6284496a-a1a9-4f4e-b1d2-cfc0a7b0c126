#!/usr/bin/env python3
"""
队列大小优化测试工具
通过测试不同队列大小下的性能表现，找到最优的max_queue_size设置
"""

import asyncio
import aiohttp
import argparse
import json
import time
import statistics
import logging
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
import numpy as np

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class QueueTestResult:
    """队列测试结果"""
    queue_size: int
    concurrent_requests: int
    total_requests: int
    successful_requests: int
    rejected_requests: int
    timeout_requests: int
    avg_response_time: float
    p95_response_time: float
    p99_response_time: float
    avg_queue_wait_time: float
    max_queue_wait_time: float
    success_rate: float
    rejection_rate: float
    user_satisfaction_score: float

class QueueSizeOptimizer:
    """队列大小优化器"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.test_data = self._generate_test_data()
        
    def _generate_test_data(self) -> List[str]:
        """生成测试数据"""
        return [
            "这是一个测试文本，用于向量化处理。",
            "人工智能技术正在快速发展，改变着我们的生活方式。",
            "机器学习模型需要大量的训练数据来提高准确性。",
            "自然语言处理是人工智能的重要分支之一。",
            "深度学习在图像识别领域取得了显著成果。",
        ] * 20  # 扩展数据量

    async def update_queue_config(self, max_queue_size: int, max_concurrent: int = None) -> bool:
        """更新队列配置"""
        try:
            config = {"max_queue_size": max_queue_size}
            if max_concurrent:
                config["max_concurrent_requests"] = max_concurrent
                
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/config",
                    json=config,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        logger.info(f"队列配置已更新: max_queue_size={max_queue_size}")
                        return True
                    else:
                        logger.error(f"配置更新失败: HTTP {response.status}")
                        return False
        except Exception as e:
            logger.error(f"配置更新异常: {str(e)}")
            return False

    async def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/stats",
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        return await response.json()
        except:
            pass
        return {}

    async def send_burst_requests(self, num_requests: int, 
                                concurrent_level: int) -> List[Dict[str, Any]]:
        """发送突发请求测试队列行为"""
        results = []
        
        async def send_single_request(session, request_id):
            start_time = time.time()
            try:
                payload = {
                    "input": [f"队列测试请求 {request_id}: " + 
                             np.random.choice(self.test_data)],
                    "model": "bge-m3"
                }
                
                async with session.post(
                    f"{self.base_url}/embeddings",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=120)  # 长超时以观察排队
                ) as response:
                    end_time = time.time()
                    response_time = end_time - start_time
                    
                    result = {
                        "request_id": request_id,
                        "start_time": start_time,
                        "end_time": end_time,
                        "response_time": response_time,
                        "status_code": response.status,
                        "success": response.status == 200,
                        "rejected": response.status == 503,
                        "timeout": False
                    }
                    
                    if response.status == 200:
                        # 成功响应
                        pass
                    elif response.status == 503:
                        # 服务繁忙（队列满）
                        error_data = await response.text()
                        result["error"] = error_data
                    else:
                        # 其他错误
                        result["error"] = await response.text()
                    
                    return result
                    
            except asyncio.TimeoutError:
                return {
                    "request_id": request_id,
                    "start_time": start_time,
                    "end_time": time.time(),
                    "response_time": time.time() - start_time,
                    "status_code": 0,
                    "success": False,
                    "rejected": False,
                    "timeout": True,
                    "error": "Request timeout"
                }
            except Exception as e:
                return {
                    "request_id": request_id,
                    "start_time": start_time,
                    "end_time": time.time(),
                    "response_time": time.time() - start_time,
                    "status_code": 0,
                    "success": False,
                    "rejected": False,
                    "timeout": False,
                    "error": str(e)
                }

        # 创建信号量控制并发
        semaphore = asyncio.Semaphore(concurrent_level)
        
        async def bounded_request(session, request_id):
            async with semaphore:
                return await send_single_request(session, request_id)

        async with aiohttp.ClientSession() as session:
            # 同时发送所有请求
            tasks = [bounded_request(session, i) for i in range(num_requests)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
        # 过滤异常结果
        valid_results = [r for r in results if isinstance(r, dict)]
        return valid_results

    def analyze_queue_performance(self, results: List[Dict[str, Any]], 
                                queue_size: int, concurrent_requests: int) -> QueueTestResult:
        """分析队列性能"""
        total_requests = len(results)
        successful_requests = sum(1 for r in results if r.get("success", False))
        rejected_requests = sum(1 for r in results if r.get("rejected", False))
        timeout_requests = sum(1 for r in results if r.get("timeout", False))
        
        success_rate = successful_requests / total_requests if total_requests > 0 else 0
        rejection_rate = rejected_requests / total_requests if total_requests > 0 else 0
        
        # 计算响应时间统计
        successful_results = [r for r in results if r.get("success", False)]
        if successful_results:
            response_times = [r["response_time"] for r in successful_results]
            avg_response_time = statistics.mean(response_times)
            p95_response_time = np.percentile(response_times, 95)
            p99_response_time = np.percentile(response_times, 99)
            
            # 估算队列等待时间（假设处理时间相对稳定）
            # 队列等待时间 ≈ 总响应时间 - 基准处理时间
            baseline_processing_time = min(response_times)  # 最快响应作为基准
            queue_wait_times = [max(0, rt - baseline_processing_time) for rt in response_times]
            avg_queue_wait_time = statistics.mean(queue_wait_times)
            max_queue_wait_time = max(queue_wait_times)
        else:
            avg_response_time = 0
            p95_response_time = 0
            p99_response_time = 0
            avg_queue_wait_time = 0
            max_queue_wait_time = 0
        
        # 计算用户满意度评分
        # 考虑成功率、响应时间和拒绝率
        satisfaction_score = self._calculate_user_satisfaction(
            success_rate, avg_response_time, rejection_rate
        )
        
        return QueueTestResult(
            queue_size=queue_size,
            concurrent_requests=concurrent_requests,
            total_requests=total_requests,
            successful_requests=successful_requests,
            rejected_requests=rejected_requests,
            timeout_requests=timeout_requests,
            avg_response_time=avg_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            avg_queue_wait_time=avg_queue_wait_time,
            max_queue_wait_time=max_queue_wait_time,
            success_rate=success_rate,
            rejection_rate=rejection_rate,
            user_satisfaction_score=satisfaction_score
        )

    def _calculate_user_satisfaction(self, success_rate: float, 
                                   avg_response_time: float, 
                                   rejection_rate: float) -> float:
        """计算用户满意度评分"""
        # 成功率权重: 50%
        success_score = success_rate
        
        # 响应时间权重: 30% (1秒内满分，超过10秒为0)
        response_score = max(0, 1 - (avg_response_time - 1) / 9)
        
        # 拒绝率权重: 20% (拒绝率越低越好)
        rejection_score = 1 - rejection_rate
        
        satisfaction = (success_score * 0.5 + 
                       response_score * 0.3 + 
                       rejection_score * 0.2)
        
        return max(0, min(1, satisfaction))

    async def test_queue_size(self, queue_size: int, 
                            test_requests: int = 100,
                            concurrent_level: int = 50) -> QueueTestResult:
        """测试特定队列大小的性能"""
        logger.info(f"测试队列大小: {queue_size}")
        
        # 更新配置
        if not await self.update_queue_config(queue_size):
            raise Exception(f"无法更新队列大小到 {queue_size}")
        
        # 等待配置生效
        await asyncio.sleep(1)
        
        # 发送测试请求
        results = await self.send_burst_requests(test_requests, concurrent_level)
        
        # 分析结果
        analysis = self.analyze_queue_performance(results, queue_size, concurrent_level)
        
        logger.info(f"队列大小 {queue_size} 测试完成:")
        logger.info(f"  成功率: {analysis.success_rate:.2%}")
        logger.info(f"  拒绝率: {analysis.rejection_rate:.2%}")
        logger.info(f"  平均响应时间: {analysis.avg_response_time:.3f}s")
        logger.info(f"  用户满意度: {analysis.user_satisfaction_score:.3f}")
        
        return analysis

    async def optimize_queue_size(self, 
                                queue_sizes: List[int] = None,
                                test_requests: int = 100,
                                concurrent_level: int = 50) -> Dict[str, Any]:
        """优化队列大小"""
        if queue_sizes is None:
            # 默认测试范围：从小到大
            queue_sizes = [10, 25, 50, 75, 100, 150, 200, 300, 500]
        
        logger.info(f"开始队列大小优化测试，测试范围: {queue_sizes}")
        
        results = []
        
        for queue_size in queue_sizes:
            try:
                result = await self.test_queue_size(
                    queue_size, test_requests, concurrent_level
                )
                results.append(result)
                
                # 短暂等待让服务恢复
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"测试队列大小 {queue_size} 时出错: {str(e)}")
                continue
        
        # 分析最优配置
        optimization_result = self._find_optimal_queue_size(results)
        
        return {
            "test_results": [self._result_to_dict(r) for r in results],
            "optimization": optimization_result,
            "recommendations": self._generate_queue_recommendations(results)
        }

    def _result_to_dict(self, result: QueueTestResult) -> Dict[str, Any]:
        """将结果转换为字典"""
        return {
            "queue_size": result.queue_size,
            "concurrent_requests": result.concurrent_requests,
            "total_requests": result.total_requests,
            "successful_requests": result.successful_requests,
            "rejected_requests": result.rejected_requests,
            "timeout_requests": result.timeout_requests,
            "avg_response_time": result.avg_response_time,
            "p95_response_time": result.p95_response_time,
            "p99_response_time": result.p99_response_time,
            "avg_queue_wait_time": result.avg_queue_wait_time,
            "max_queue_wait_time": result.max_queue_wait_time,
            "success_rate": result.success_rate,
            "rejection_rate": result.rejection_rate,
            "user_satisfaction_score": result.user_satisfaction_score
        }

    def _find_optimal_queue_size(self, results: List[QueueTestResult]) -> Dict[str, Any]:
        """找到最优队列大小"""
        if not results:
            return {}
        
        # 按用户满意度排序
        sorted_results = sorted(results, key=lambda x: x.user_satisfaction_score, reverse=True)
        best_result = sorted_results[0]
        
        # 找到满足条件的最小队列大小
        acceptable_results = [
            r for r in results 
            if r.success_rate >= 0.95 and r.user_satisfaction_score >= 0.8
        ]
        
        if acceptable_results:
            min_acceptable = min(acceptable_results, key=lambda x: x.queue_size)
        else:
            min_acceptable = best_result
        
        return {
            "optimal_queue_size": best_result.queue_size,
            "optimal_satisfaction_score": best_result.user_satisfaction_score,
            "optimal_success_rate": best_result.success_rate,
            "optimal_avg_response_time": best_result.avg_response_time,
            "minimum_acceptable_queue_size": min_acceptable.queue_size,
            "minimum_acceptable_satisfaction": min_acceptable.user_satisfaction_score,
            "performance_curve": [
                {
                    "queue_size": r.queue_size,
                    "satisfaction_score": r.user_satisfaction_score,
                    "success_rate": r.success_rate,
                    "rejection_rate": r.rejection_rate
                }
                for r in sorted(results, key=lambda x: x.queue_size)
            ]
        }

    def _generate_queue_recommendations(self, results: List[QueueTestResult]) -> List[str]:
        """生成队列配置建议"""
        if not results:
            return ["无测试数据，无法生成建议"]
        
        recommendations = []
        
        # 找到最优结果
        best_result = max(results, key=lambda x: x.user_satisfaction_score)
        
        recommendations.append(
            f"推荐队列大小: {best_result.queue_size} "
            f"(用户满意度: {best_result.user_satisfaction_score:.3f})"
        )
        
        # 分析拒绝率
        high_rejection_results = [r for r in results if r.rejection_rate > 0.1]
        if high_rejection_results:
            min_queue_with_rejection = min(high_rejection_results, key=lambda x: x.queue_size)
            recommendations.append(
                f"队列大小小于 {min_queue_with_rejection.queue_size} 时会出现较高拒绝率"
            )
        
        # 分析响应时间
        slow_response_results = [r for r in results if r.avg_response_time > 3.0]
        if slow_response_results:
            min_queue_with_slow_response = min(slow_response_results, key=lambda x: x.queue_size)
            recommendations.append(
                f"队列大小超过 {min_queue_with_slow_response.queue_size} 时响应时间会显著增加"
            )
        
        # 资源使用建议
        max_tested_queue = max(results, key=lambda x: x.queue_size)
        recommendations.append(
            f"每个排队请求约占用 1-2KB 内存，队列大小 {max_tested_queue.queue_size} "
            f"约需要 {max_tested_queue.queue_size * 2}KB 队列内存"
        )
        
        # 业务场景建议
        recommendations.extend([
            "对于实时性要求高的场景，建议选择较小的队列大小以快速拒绝过载请求",
            "对于批处理场景，可以选择较大的队列大小以提高吞吐量",
            "建议结合监控告警，当队列使用率超过80%时考虑扩容",
            "生产环境建议预留20-30%的队列容量作为突发流量缓冲"
        ])
        
        return recommendations

    def save_results(self, results: Dict[str, Any], filename: str = None):
        """保存测试结果"""
        if filename is None:
            filename = f"queue_optimization_results_{int(time.time())}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"队列优化结果已保存到 {filename}")

async def main():
    parser = argparse.ArgumentParser(description="队列大小优化测试")
    parser.add_argument("--url", default="http://localhost:36001", 
                       help="服务URL")
    parser.add_argument("--queue-sizes", nargs="+", type=int,
                       help="要测试的队列大小列表")
    parser.add_argument("--test-requests", type=int, default=100,
                       help="每次测试的请求数")
    parser.add_argument("--concurrent-level", type=int, default=50,
                       help="并发级别")
    parser.add_argument("--output", help="结果输出文件")
    
    args = parser.parse_args()
    
    optimizer = QueueSizeOptimizer(args.url)
    
    # 检查服务是否可用
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{args.url}/health", timeout=aiohttp.ClientTimeout(total=5)) as response:
                if response.status != 200:
                    logger.error("服务不可用")
                    return
    except:
        logger.error("无法连接到服务")
        return
    
    logger.info("开始队列大小优化测试...")
    
    # 运行优化测试
    results = await optimizer.optimize_queue_size(
        queue_sizes=args.queue_sizes,
        test_requests=args.test_requests,
        concurrent_level=args.concurrent_level
    )
    
    # 打印结果摘要
    print("\n" + "="*60)
    print("队列大小优化测试结果")
    print("="*60)
    
    if results.get("optimization"):
        opt = results["optimization"]
        print(f"最优队列大小: {opt['optimal_queue_size']}")
        print(f"最优用户满意度: {opt['optimal_satisfaction_score']:.3f}")
        print(f"最优成功率: {opt['optimal_success_rate']:.2%}")
        print(f"最优平均响应时间: {opt['optimal_avg_response_time']:.3f}s")
        print(f"最小可接受队列大小: {opt['minimum_acceptable_queue_size']}")
    
    if results.get("recommendations"):
        print(f"\n关键建议:")
        for i, rec in enumerate(results["recommendations"][:5], 1):
            print(f"{i}. {rec}")
    
    # 保存详细结果
    output_file = args.output or f"queue_optimization_results_{int(time.time())}.json"
    optimizer.save_results(results, output_file)
    print(f"\n详细结果已保存到: {output_file}")

if __name__ == "__main__":
    asyncio.run(main())
