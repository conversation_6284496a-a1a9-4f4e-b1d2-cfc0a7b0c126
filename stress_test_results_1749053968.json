{"summary": {"total_requests": 9065, "successful_requests": 7180, "timeout_requests": 1846, "success_rate": 0.7920573634859349, "timeout_rate": 0.2036403750689465, "avg_response_time": 1.4046713107475663, "median_response_time": 1.7199848890304565, "p95_response_time": 2.674939513206481, "p99_response_time": 2.817322299480438, "min_response_time": 0.11305642127990723, "max_response_time": 9.608479976654053}, "by_rps": {"5": {"target_rps": 5, "total_requests": 3085, "successful_requests": 3083, "timeout_requests": 0, "success_rate": 0.9993517017828201, "timeout_rate": 0.0, "avg_response_time": 0.357154564620843, "p95_response_time": 0.9174533605575552, "actual_throughput": 5.1383197636313644}, "10": {"target_rps": 10, "total_requests": 5980, "successful_requests": 4097, "timeout_requests": 1846, "success_rate": 0.6851170568561873, "timeout_rate": 0.30869565217391304, "avg_response_time": 2.1929295798002117, "p95_response_time": 2.7230560302734377, "actual_throughput": 6.756901218568738}}, "recommendations": {"max_sustainable_rps": 5, "max_throughput_per_instance": 5.1383197636313644, "estimated_instances_for_2000_users": 7, "recommended_safe_rps": 4, "safety_margin": "建议使用80%的最大承载能力作为安全运行点", "queue_size_recommendations": {"max_queue_size": 10, "explanation": "队列大小应能缓冲短期突发流量，建议设置为最大RPS的2倍"}, "monitoring_recommendations": ["监控每秒请求数，确保不超过 4 RPS", "监控超时率，确保低于5%", "监控队列长度，避免积压过多请求", "监控响应时间P95，确保在可接受范围内", "设置自动扩缩容策略，基于队列长度和响应时间", "定期进行吞吐量测试验证容量"], "scaling_strategy": {"scale_up_threshold": "队列长度超过 5 或响应时间P95超过5秒", "scale_down_threshold": "队列长度低于 2 且响应时间P95低于2秒", "min_instances": 2, "max_instances": 10}}, "max_sustainable_rps": 5}