{"summary": {"total_requests": 8999, "successful_requests": 7079, "timeout_requests": 1882, "success_rate": 0.7866429603289254, "timeout_rate": 0.20913434826091787, "avg_response_time": 1.531324068289858, "median_response_time": 1.889631748199463, "p95_response_time": 2.9353824138641356, "p99_response_time": 3.0737310123443606, "min_response_time": 0.1124715805053711, "max_response_time": 10.039396286010742}, "by_rps": {"5": {"target_rps": 5, "total_requests": 2947, "successful_requests": 2947, "timeout_requests": 0, "success_rate": 1.0, "timeout_rate": 0.0, "avg_response_time": 0.3113406846836376, "p95_response_time": 0.7255589962005615, "actual_throughput": 4.912234778809602}, "10": {"target_rps": 10, "total_requests": 6052, "successful_requests": 4132, "timeout_requests": 1882, "success_rate": 0.6827495042961005, "timeout_rate": 0.3109715796430932, "avg_response_time": 2.4014332240225618, "p95_response_time": 2.993388628959656, "actual_throughput": 6.821491022197005}}, "recommendations": {"max_sustainable_rps": 5, "max_throughput_per_instance": 4.912234778809602, "estimated_instances_for_2000_users": 7, "recommended_safe_rps": 4, "safety_margin": "建议使用80%的最大承载能力作为安全运行点", "queue_size_recommendations": {"max_queue_size": 10, "explanation": "队列大小应能缓冲短期突发流量，建议设置为最大RPS的2倍"}, "monitoring_recommendations": ["监控每秒请求数，确保不超过 4 RPS", "监控超时率，确保低于5%", "监控队列长度，避免积压过多请求", "监控响应时间P95，确保在可接受范围内", "设置自动扩缩容策略，基于队列长度和响应时间", "定期进行吞吐量测试验证容量"], "scaling_strategy": {"scale_up_threshold": "队列长度超过 5 或响应时间P95超过5秒", "scale_down_threshold": "队列长度低于 2 且响应时间P95低于2秒", "min_instances": 2, "max_instances": 10}}, "max_sustainable_rps": 5}