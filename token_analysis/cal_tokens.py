from transformers import AutoTokenizer

tokenizer = AutoTokenizer.from_pretrained(
    "./model", use_fast=True, trust_remote_code=True
)


def count_characters_in_sliding_window(filePath, maxLine):
    """
    读取文件并使用滑动窗口计算字符数

    参数:
    filePath (str): 文件路径
    maxLine (int): 滑动窗口大小（行数）

    返回:
    list: 每个滑动窗口的字符数列表
    """
    window_char_counts = []
    token_count = []

    try:
        with open(filePath, 'r', encoding='utf-8') as file:
            lines = file.readlines()
            # 如果文件行数小于maxLine，则只计算一次
            for i in range(len(lines) - maxLine + 1):
                window = lines[i:i + maxLine]
                char_count = sum(len(line) for line in window)
                window_char_counts.append(char_count)
                token_num = len(tokenizer.encode(''.join(window)))
                token_count.append(token_num)

                # 打印当前窗口信息（可选）
                # print(f"窗口 {i + 1}: 行 {i + 1}-{i + maxLine}, 字符数: {char_count}")
            print(f"{maxLine}行平均包含字符个数：", sum(
                window_char_counts)/len(window_char_counts))
            print(f"{maxLine}行平均token数：{sum(token_count)/len(token_count)}")
            print(f"{maxLine}行最大token数：{max(token_count)}")
    except FileNotFoundError:
        print(f"错误: 文件 {filePath} 未找到")
    except Exception as e:
        print(f"发生错误: {e}")

    return window_char_counts


# 示例用法
if __name__ == "__main__":
    file_paths = {
        "C": "./code/ikev2_format.c",
        "python": "./code/repo_understander.py",
        "typescript": "./code/index.ts"
    }
    max_line = 50
    for repo in file_paths.keys():
        print(repo)
        path = file_paths.get(repo, "")
        count_characters_in_sliding_window(path, max_line)
