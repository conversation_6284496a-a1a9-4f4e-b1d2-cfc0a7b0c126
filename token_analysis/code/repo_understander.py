"Code search"

import fnmatch
import hashlib
import json
import mimetypes
import os
import traceback
from pathlib import Path
import platform
import re
import time
from typing import Dict
import uuid

import lancedb
from llama_index.core import QueryBundle
from llama_index.core import VectorStoreIndex
from llama_index.core.ingestion import DocstoreStrategy, IngestionPipeline
from llama_index.core.postprocessor import SimilarityPostprocessor
from llama_index.core.retrievers import BaseRetriever
from llama_index.core.schema import TextNode
from llama_index.core.storage.docstore import SimpleDocumentStore
from llama_index.core.vector_stores import MetadataFilters, ExactMatchFilter
from llama_index.llms.vllm import VllmServer
from llama_index.vector_stores.lancedb import LanceDBVectorStore
from loguru import logger
from tqdm import tqdm

from src.domain.subdomains.repo_understander.bm25_retriever import BM25Retriever
from src.domain.subdomains.repo_understander.constants import (
    DEFAULT_TEMPERATURE,
    DEFAULT_NEW_TOKENS,
    DEFAULT_STOP_STR,
    DEFAULT_RERANK_NUM,
    DEFAULT_DOCSOTRE,
    DEFAULT_PATH_DOCSOTRE,
    DEFAULT_VECTOR_PATH,
    DEFAULT_CACHE_DIR,
)
from src.domain.subdomains.repo_understander.index import (
    CodeSplitter,
    supported_language_parser,
)
from src.domain.subdomains.repo_understander.index.codeblocks import (
    CodeBlock,
    CodeBlockType,
)
from src.domain.subdomains.repo_understander.index.dependency import JavaDependencyRetriever
from src.domain.subdomains.repo_understander.index.parser.cpp import CppParser
from src.domain.subdomains.repo_understander.tools import (
    CodeExplanation,
    attach_line_numbers,
    ZeroAgentEmbedding,
    SentenceTransformerRerank,
    SimpleDirectoryReader,
    list_files_without_test,
    ClassFuncRetriever,
)
from src.infrastructure.prompt.repo_understander_prompt import (
    SYSTEM_PROMPT,
    EXTRACT_SEARCH_INFO_PROMPT,
)


class HybridRetriever(BaseRetriever):
    """Hybrid retrieve."""

    def __init__(self, vector_retriever, bm25_retriever):
        self.vector_retriever = vector_retriever
        self.bm25_retriever = bm25_retriever
        super().__init__()

    def _retrieve(self, query, **kwargs):
        bm25_nodes = self.bm25_retriever.retrieve(query, **kwargs)
        vector_nodes = self.vector_retriever.retrieve(query, **kwargs)

        # combine the two lists of nodes
        all_nodes = []
        node_ids = set()
        for node in bm25_nodes + vector_nodes:
            if node.node.node_id not in node_ids:
                all_nodes.append(node)
                node_ids.add(node.node.node_id)
        return all_nodes


class BM25RetrieverManager:
    """BM25 retriever manager."""

    def __init__(self):
        self.items = {}

    def add_retriever(self, repo_name, retriever):
        """Add retriever."""
        if isinstance(retriever, BaseRetriever):
            self.items[repo_name] = retriever
        else:
            raise ValueError("Retriever must be an instance of BaseRetriever")

    def get_retriever(self, repo_name):
        """Get retriever."""
        return self.items.get(repo_name, None)

    def list_items(self):
        """List retrievers."""
        return list(self.items.keys())


class EngineConfig:
    """Configuration class to hold various external configurations for RAGEngine."""

    def __init__(self):
        self.redis_host = os.getenv("REDIS_HOST")
        self.redis_port = os.getenv("REDIS_PORT")
        self.qdrant_url = os.getenv("QDRANT_URL")
        self.llm_url = os.getenv("LLM_URL")
        self.embedding_url = os.getenv("EMBED_URL")
        self.reranker_url = os.getenv("RERANK_URL")


class RepoUnderstander:
    """RepoUnderstander is responsible for indexing and retrieving information from code repositories."""

    def __init__(self) -> None:
        self.config = EngineConfig()

        self.llm = VllmServer(
            api_url=self.config.llm_url, temperature=DEFAULT_TEMPERATURE,
            max_new_tokens=DEFAULT_NEW_TOKENS, stop=[DEFAULT_STOP_STR])
        self.embed_model = ZeroAgentEmbedding(url=self.config.embedding_url)
        self.cutoff_postprocessor = SimilarityPostprocessor(
            similarity_cutoff=0.6)
        self.bm25_manager = BM25RetrieverManager()
        self.class_func_retriever = ClassFuncRetriever()

        self._blocks_by_function_name = {}
        self._blocks_by_class_name = {}
        self._blocks_by_code_block = {}

        os.makedirs(DEFAULT_CACHE_DIR, exist_ok=True)

    def create_index(self, repo_path, language, interpret=False):
        """
        Creates an index for the specified code repository.

        Args:
            repo_path (str): Path to the repository.
            repo_name (str): Name of the repository.
            language (str): Programming language of the files to index.

        Returns:
            dict: Information about the indexing operation, including its status and duration.
        """
        index_id = str(uuid.uuid4())
        start_index = time.time()

        repo_name = os.path.basename(os.path.normpath(repo_path))
        logger.info(
            f"[{index_id}] Repo Path: {repo_path}, Repo Name: {repo_name}")
        try:

            documents = self._load_documents(repo_path, language)

            index_begin = time.time()
            self._transfrom_nodes(documents, repo_path, interpret)
            index_finish = time.time()
            logger.info(
                f"[{index_id}] [Index Time]: {index_finish-index_begin:.2f}s")

            if "java" in language:
                self._handle_java_dependency_mapping(repo_path)

            result = "Index Successed"
        except Exception as err:
            logger.error(f"[{index_id}]: {err}")
            logger.error(traceback.format_exc())
            result = "Index Failed"
            raise

        end_index = time.time()
        index_output = {
            "code": 1,
            "iid": index_id,
            "time": end_index - start_index,
            "result": result,
        }
        logger.info("Index completed!")
        return index_output

    def _initialize_stores(self, repo_name):
        repo_hash = hashlib.md5(repo_name.encode()).hexdigest()
        doc_path = os.path.join(DEFAULT_CACHE_DIR, repo_hash, DEFAULT_DOCSOTRE)
        path_doc_path = os.path.join(
            DEFAULT_CACHE_DIR, repo_hash, DEFAULT_PATH_DOCSOTRE)

        if os.path.exists(doc_path):
            docstore = SimpleDocumentStore.from_persist_path(
                doc_path, namespace=repo_name)
        else:
            docstore = SimpleDocumentStore(namespace=repo_name)

        if os.path.exists(path_doc_path):
            path_docstore = SimpleDocumentStore.from_persist_path(
                path_doc_path, namespace=f"{repo_name}_path")
        else:
            path_docstore = SimpleDocumentStore(namespace=f"{repo_name}_path")

        vector_store = LanceDBVectorStore(
            uri=DEFAULT_VECTOR_PATH,
            table_name=repo_name,
            mode="overwrite",
            query_type="hybrid"
        )
        path_vector_store = LanceDBVectorStore(
            uri=DEFAULT_VECTOR_PATH,
            table_name=f"{repo_name}_path",
            mode="overwrite",
            query_type="hybrid"
        )
        return docstore, path_docstore, vector_store, path_vector_store

    def _load_documents(self, repo_path, language):
        def file_metadata_func(file_path: str) -> Dict:
            is_windows = platform.system().lower() == "windows"
            if is_windows and file_path.startswith(r"\\?"):
                file_path = file_path[4:]

            file_path = file_path.replace(repo_path, "")
            if file_path.startswith("/") or file_path.startswith("\\"):
                file_path = file_path[1:]

            test_patterns = [
                "**/test_*",
                "**/*_test*",
                "**/Test*",
                "**/*Test*"
            ]
            category = (
                "test"
                if any(fnmatch.fnmatch(file_path, pattern) for pattern in test_patterns)
                else "implementation"
            )

            return {
                "file_path": file_path,
                "file_name": os.path.basename(file_path),
                "file_type": mimetypes.guess_type(file_path)[0],
                "category": category,
            }

        try:
            documents = SimpleDirectoryReader(
                input_dir=repo_path,
                file_metadata=file_metadata_func,
                filename_as_id=True,
                required_exts=[language],
                recursive=True
            ).load_data(show_progress=True)
            return documents
        except Exception as err:
            logger.error(f"error: {err}")
            logger.error(traceback.format_exc())
            raise

    def _transfrom_nodes(self, documents, repo_path, interpret):
        repo_classes = {}
        repo_functions = {}
        repo_name = os.path.basename(os.path.normpath(repo_path))
        repo_hash = hashlib.md5(repo_name.encode()).hexdigest()
        persist_dir = os.path.join(DEFAULT_CACHE_DIR, repo_hash)
        os.makedirs(persist_dir, exist_ok=True)
        repo_class_file = os.path.join(persist_dir,
                                       "blocks_by_class_name.json")
        repo_func_file = os.path.join(persist_dir,
                                      "blocks_by_function_name.json")

        if os.path.exists(repo_class_file):
            with open(repo_class_file) as f:
                blocks_by_class_name = json.load(f)
                repo_classes = blocks_by_class_name[repo_name]
        if os.path.exists(repo_func_file):
            with open(repo_func_file) as f:
                blocks_by_function_name = json.load(f)
                repo_functions = blocks_by_function_name[repo_name]

        def index_callback(codeblock: CodeBlock):
            if (codeblock.type == CodeBlockType.CLASS) and ("None" not in codeblock.identifier):
                if codeblock.identifier not in repo_classes:
                    repo_classes[codeblock.identifier] = []
                repo_classes[codeblock.identifier].append(
                    (codeblock.module.file_path, codeblock.full_path())
                )

            if (codeblock.type == CodeBlockType.FUNCTION) and ("None" not in codeblock.identifier):
                if codeblock.identifier not in repo_functions:
                    repo_functions[codeblock.identifier] = []
                repo_functions[codeblock.identifier].append(
                    (codeblock.module.file_path, codeblock.full_path())
                )

            repo_codeblocks = self._blocks_by_code_block.get(repo_name, {})
            repo_codeblocks[codeblock.module.file_path] = codeblock.module

        docstore, path_docstore, vector_store, path_vector_store = self._initialize_stores(
            repo_name)

        # Started by AICoder, pid:iafd0vf633401f11488109b9e052ce14a3a3a717
        path_index = VectorStoreIndex.from_vector_store(
            vector_store=path_vector_store, embed_model=self.embed_model)
        repo_files = list_files_without_test(repo_path)
        logger.info(f"Found {len(repo_files)} files without test...")
        path_nodes = [TextNode(text=file) for file in tqdm(
            repo_files, desc="Loading repo files...")]
        path_index.insert_nodes(path_nodes, show_progress=True)
        # Ended by AICoder, pid:iafd0vf633401f11488109b9e052ce14a3a3a717

        transformations = [
            CodeSplitter(repo_path=repo_path,
                         index_callback=index_callback),
            self.embed_model,
        ]

        if interpret:
            transformations = [
                CodeSplitter(repo_path=repo_path,
                             index_callback=index_callback),
                CodeExplanation(llm=self.llm),
                self.embed_model,
            ]

        pipeline = IngestionPipeline(
            transformations=transformations,
            docstore=docstore,
            vector_store=vector_store,
            docstore_strategy=DocstoreStrategy.UPSERTS_AND_DELETE,
        )
        try:
            self._clear_class_and_func_info(docstore,
                                            documents,
                                            repo_classes,
                                            repo_functions)
            nodes = pipeline.run(show_progress=True,
                                 documents=documents, store_doc_text=False)
            logger.info(f"Nodes Num: {len(nodes)}")
            pipeline.persist(persist_dir=persist_dir,
                             docstore_name=DEFAULT_DOCSOTRE)
            self._update_path_store(vector_store, path_docstore, persist_dir)
            self._blocks_by_class_name[repo_name] = repo_classes
            self._blocks_by_function_name[repo_name] = repo_functions
            self.persist_class_and_func_info(persist_dir)
        except Exception as err:
            logger.error(err)
            raise

    # Started by AICoder, pid:bc3eee020ej9b67143810847e07858478aa4384b
    def _update_path_store(self, vector_store, path_docstore, persist_dir):
        all_doc_ids = list(path_docstore.docs.keys())
        for doc_id in all_doc_ids:
            path_docstore.delete_document(doc_id)
        nodes = vector_store.get_nodes()
        path_nodes = [
            TextNode(text=node.metadata['file_path'])
            for node in tqdm(nodes, desc="Construct path node")
        ]
        path_docstore.add_documents(path_nodes)
        path_docstore.persist(os.path.join(persist_dir, DEFAULT_PATH_DOCSOTRE))

    def _clear_class_and_func_info(
            self,
            docstore,
            documents,
            repo_classes,
            repo_functions
    ):
        if not repo_classes and not repo_functions:
            return
        doc_ids_from_nodes = set()
        nodes_to_clear = {}
        for node in documents:
            ref_doc_id = node.ref_doc_id if node.ref_doc_id else node.id_
            doc_ids_from_nodes.add(ref_doc_id)
            existing_hash = docstore.get_document_hash(ref_doc_id)
            if existing_hash and existing_hash != node.hash:
                nodes_to_clear[ref_doc_id] = node

        existing_doc_ids_before = set(
            docstore.get_all_document_hashes().values())
        doc_ids_to_delete = existing_doc_ids_before - doc_ids_from_nodes
        for ref_doc_id in doc_ids_to_delete:
            # Started by AICoder, pid:k3213ga7c4s6eb714dc90945902a4d0b5255900f
            try:
                node = docstore.get_document(ref_doc_id)
                nodes_to_clear[ref_doc_id] = node
            except Exception as err:
                logger.info(err)
            # Ended by AICoder, pid:k3213ga7c4s6eb714dc90945902a4d0b5255900f
        file_path_set = set([node.metadata['file_path']
                            for node in nodes_to_clear.values()])

        def clear_repo_map(repo_dict):
            for key, value in repo_dict.copy().items():
                for file_path, _ in value:
                    if file_path in file_path_set:
                        repo_dict.pop(key)
                        break

        clear_repo_map(repo_classes)
        clear_repo_map(repo_functions)
    # Ended by AICoder, pid:bc3eee020ej9b67143810847e07858478aa4384b

    def _handle_java_dependency_mapping(self, repo_path):
        dependency_retriever = JavaDependencyRetriever(repo_path)
        dependency_retriever.generate_dependency_map()
        dependency_retriever.save_dependency_to_cache()

    def retrieve(
            self,
            repo_path,
            query="",
            file_path="",
            top_n=DEFAULT_RERANK_NUM,
            with_num=False,
    ):
        """
        Retrieves relevant nodes from the repository based on specified criteria.

        Args:
            repo_path (str): Path of the repository.
            query (str, optional): Search query.
            file_path (str, optional): Path of the specific file to search.

        Returns:
            dict: Information about the retrieval operation, including its status, duration, and result context.
        """
        retrieve_id = str(uuid.uuid4())
        start_retrieve = time.time()

        repo_name = os.path.basename(os.path.normpath(repo_path))
        logger.info(
            f"repo_name: {repo_name}; query: {query}; file_path: {file_path}; "
            f"top_n: {top_n}; with_num: {with_num}"
        )

        if not any([query, file_path]):
            raise TypeError(
                ("Please provide at least one parameter from query, file_path"))

        retrieved_nodes = []

        _, path_docstore, vector_store, _ = self._initialize_stores(
            repo_name)
        index = VectorStoreIndex.from_vector_store(
            vector_store=vector_store, embed_model=self.embed_model)

        retrieved_nodes.extend(self._retrieve_by_file_path(
            file_path, retrieve_id, repo_name, path_docstore, index) if file_path else [])
        retrieved_nodes.extend(self._retrieve_by_query(
            query, retrieve_id, index, top_n) if query else [])
        output_nodes = self._deduplicate_and_sort(retrieved_nodes)

        context_str = self._build_context_with_dependencies(
            output_nodes, repo_path, with_num)

        end_retrieve = time.time()
        retrieve_output = {
            "code": 1,
            "rid": retrieve_id,
            "time": end_retrieve - start_retrieve,
            "result": context_str,
        }
        logger.info(f"[{retrieve_id}][Retrieve Output]:\n{retrieve_output}")

        return retrieve_output

    def _retrieve_by_file_path(self, file_path, retrieve_id, repo_name, path_docstore, index):
        logger.info(f"[{retrieve_id}][Init File Path]: {file_path}")
        repo_path_name = f"{repo_name}_path"
        path_retriever = self.bm25_manager.get_retriever(repo_path_name)
        if not path_retriever:
            path_retriever = BM25Retriever.from_defaults(
                docstore=path_docstore, similarity_top_k=1)
            self.bm25_manager.add_retriever(repo_path_name, path_retriever)

        retrieved_node = path_retriever.retrieve(file_path)
        file_path = retrieved_node[0].get_content()
        logger.info(f"[{retrieve_id}][Fuzzy Search File Path]: {file_path}")
        file_filters = MetadataFilters(
            filters=[ExactMatchFilter(key="file_path", value=file_path)])
        file_retriever = index.as_retriever(
            similarity_top_k=500, filters=file_filters)
        retrieve_start = time.time()
        file_retrieved_nodes = file_retriever.retrieve(file_path)
        retrieve_finish = time.time()
        logger.info(
            f"[{retrieve_id}][FM Retrieve Time]: {retrieve_finish-retrieve_start:.2f}s"
            f"[{retrieve_id}][FM Node Num]: {len(file_retrieved_nodes)}")

        return file_retrieved_nodes

    def _retrieve_by_query(self, query, retrieve_id, index, top_n):
        try:
            query = self.llm.complete(
                SYSTEM_PROMPT.format(content=EXTRACT_SEARCH_INFO_PROMPT.format(instruction=query))).text
            query = re.sub(r'提取信息：', '', query)
            logger.info(f"[{retrieve_id}][Extract Search INFO]: {query}")

            # Retrieve
            retrieve_start = time.time()
            vector_retriever = index.as_retriever(similarity_top_k=50)
            retrieved_nodes = vector_retriever.retrieve(query)
            rerank_start = time.time()
            reranker = SentenceTransformerRerank(
                url=self.config.reranker_url, top_n=top_n)
            reranked_nodes = reranker.postprocess_nodes(
                retrieved_nodes,
                query_bundle=QueryBundle(query),
            )
            rerank_finish = time.time()
            logger.info(
                f"[{retrieve_id}][QM Retrieve time]: {rerank_start-retrieve_start:.2f}s"
                f"[{retrieve_id}][QM Rerank time]: {rerank_finish-rerank_start:.2f}s")
            logger.info(
                f"[{retrieve_id}][Reranker Node Score]:{[node.score for node in reranked_nodes]}")
            return reranked_nodes
        except Exception as err:
            logger.info(f"[{retrieve_id}]: {err}")
            raise

    # Started by AICoder, pid:q40bc92579f542414a9509e16032bb536e938729
    def retrieve_class(
        self,
        repo_path,
        class_names=None,
        with_num=False,
    ):
        if class_names is None:
            class_names = []

        retrieve_id = str(uuid.uuid4())
        start_retrieve = time.time()

        repo_name = os.path.basename(os.path.normpath(repo_path))
        logger.info(f"repo_name: {repo_name}; class_names: {class_names}")

        dependency_retriever = JavaDependencyRetriever(repo_path)
        dependency_retriever.load_dependency_from_cache()

        context_str_list = []

        for class_name in class_names:
            retrieved_class = self.class_func_retriever.retrieve_by_class_or_func_name(
                repo_path, class_name)
            class_code = retrieved_class.get("result", "").strip()
            file_path = retrieved_class.get("file_path")
            start_line = retrieved_class.get("start_line")

            if not file_path:
                continue

            if with_num:
                class_code = attach_line_numbers(class_code, start_line)

            class_path = os.path.join(repo_path, file_path)
            logger.info(f"[Retrieve Class Path]: {class_path}")

            if platform.system().lower() == "windows":
                class_path = f"\\\\?\\{class_path}"

            headers = dependency_retriever.retrieve_headers(class_path)
            import_signature = dependency_retriever.retrieve_context(
                class_path)
            dependencies = f"上述代码依赖:\n{import_signature}\n" if import_signature else ""
            class_context = (f"**{file_path}**\n"
                             f"检索代码:\n{headers}\n\n{class_code}\n{dependencies}")
            context_str_list.append(class_context)

        context_str = "\n".join(context_str_list)
        end_retrieve = time.time()

        return {
            "rid": retrieve_id,
            "time": end_retrieve - start_retrieve,
            "result": context_str,
        }
    # Ended by AICoder, pid:q40bc92579f542414a9509e16032bb536e938729

    # Started by AICoder, pid:sdd04365dcc761a148a6085f106f9c5bdf11bf13
    def retrieve_method(
        self,
        repo_path,
        class_name="",
        func_name="",
        with_num=False,
    ):
        retrieve_id = str(uuid.uuid4())
        start_retrieve = time.time()

        repo_name = os.path.basename(os.path.normpath(repo_path))
        logger.info(
            f"repo_name: {repo_name}; class_name: {class_name}; func_name: {func_name}")

        dependency_retriever = JavaDependencyRetriever(repo_path)
        dependency_retriever.load_dependency_from_cache()

        retrieved_method = self.class_func_retriever.retrieve_by_class_or_func_name(
            repo_path, class_name, func_name)
        file_path = retrieved_method.get("file_path")

        if not file_path:
            logger.warning(
                "No file path found for the specified class and function.")
            return {
                "rid": retrieve_id,
                "time": 0,
                "result": "",
            }

        method_code = retrieved_method.get("result", "").strip()
        start_line = retrieved_method.get("start_line")
        if with_num:
            method_code = attach_line_numbers(method_code, start_line)

        method_path = os.path.join(repo_path, file_path)
        logger.info(f"[Retrieve Method Path]: {method_path}")

        if platform.system().lower() == "windows":
            method_path = f"\\\\?\\{method_path}"

        headers = dependency_retriever.retrieve_headers(method_path)
        import_signature = dependency_retriever.retrieve_context(method_path)
        dependencies = f"上述代码依赖:\n{import_signature}\n" if import_signature else ""
        context_str = (f"**{file_path}**\n"
                       f"检索代码:\n{headers}\n\n{method_code}\n{dependencies}")

        end_retrieve = time.time()

        return {
            "rid": retrieve_id,
            "time": end_retrieve - start_retrieve,
            "result": context_str,
        }
    # Ended by AICoder, pid:sdd04365dcc761a148a6085f106f9c5bdf11bf13

    def persist_class_and_func_info(self, persist_dir: str):
        with open(os.path.join(persist_dir, "blocks_by_class_name.json"), "w") as f:
            f.write(json.dumps(self._blocks_by_class_name))

        with open(os.path.join(persist_dir, "blocks_by_function_name.json"), "w") as f:
            f.write(json.dumps(self._blocks_by_function_name))

    def _deduplicate_and_sort(self, nodes):
        unique_node_ids = set()
        deduplicated_nodes = []

        for node in nodes:
            if node.node_id not in unique_node_ids:
                deduplicated_nodes.append(node)
                unique_node_ids.add(node.node_id)

        return sorted(deduplicated_nodes, key=lambda node: (node.metadata['file_path'],
                                                            node.metadata['start_line']))

    # Started by AICoder, pid:i6553n8442u558014e2c0a94a0563e3e76a384b7
    def _build_context_with_dependencies(self, sorted_nodes, repo_path, with_num):
        repo_name = os.path.basename(os.path.normpath(repo_path))
        logger.info(f"repo_name: {repo_name}")

        file_paths = {node.metadata['file_path'] for node in sorted_nodes}
        logger.info(f"[Retrieve Import Context File Paths]: {file_paths}")

        context_str = ""
        dependency_retriever = JavaDependencyRetriever(repo_path)
        dependencies_loaded = dependency_retriever.load_dependency_from_cache()

        for path in file_paths:
            retrieved_code = self._group_codes_by_file(
                sorted_nodes, path, with_num)
            code = "\n......\n".join(retrieved_code)

            if dependencies_loaded:
                absolute_path = os.path.join(repo_path, path)
                import_signature = dependency_retriever.retrieve_context(
                    absolute_path)
                context_str += f"**{path}**\n检索代码:\n{code}\n上述代码依赖:\n{import_signature}\n\n"
            else:
                context_str += f"**{path}**\n检索代码:\n{code}\n\n"

        return context_str

    def _group_codes_by_file(self, sorted_nodes, path, with_num):
        retrieved_code = []
        for node in sorted_nodes:
            if node.metadata['file_path'] == path:
                code_snippet = node.metadata.get(
                    "code", node.get_content()).strip()
                if with_num:
                    code_snippet = attach_line_numbers(
                        code_snippet, node.metadata["start_line"])
                retrieved_code.append(code_snippet)
        return retrieved_code
    # Ended by AICoder, pid:i6553n8442u558014e2c0a94a0563e3e76a384b7

    def locate_code_path(self, repo_path, tasks):
        code_locations = {}
        repo_name = os.path.basename(os.path.normpath(repo_path))
        repo_hash = hashlib.md5(repo_name.encode()).hexdigest()
        path_doc_path = os.path.join(
            DEFAULT_CACHE_DIR, repo_hash, DEFAULT_PATH_DOCSOTRE)
        vector_store = LanceDBVectorStore(
            uri=DEFAULT_VECTOR_PATH,
            table_name=repo_name,
            mode="overwrite",
            query_type="hybrid"
        )
        path_docstore = SimpleDocumentStore.from_persist_path(
            path_doc_path, namespace=f"{repo_name}_path")

        ut_path_retriever = BM25Retriever.from_defaults(
            docstore=path_docstore, similarity_top_k=1)

        index = VectorStoreIndex.from_vector_store(vector_store=vector_store)
        category_filter = MetadataFilters(
            filters=[ExactMatchFilter(key="category", value="implementation")])
        vector_retriever = index.as_retriever(
            similarity_top_k=1, filters=category_filter)

        for task in tasks:
            task_name = task["name"]
            query = task["description"]
            retrieved_node = vector_retriever.retrieve(query)
            logger.info(f"retrieved node score: {retrieved_node[0].score}")
            node = self.cutoff_postprocessor.postprocess_nodes(retrieved_node)

            if node:
                path = node[0].metadata['file_path']
                file_name = Path(path).stem
                ut_like_path = path.replace(file_name, f"{file_name}Test")
                ut_path_node = ut_path_retriever.retrieve(ut_like_path)

                code_locations[task_name] = {"path": path,
                                             "utPath": ut_path_node[0].get_content()}
            else:
                code_locations[task_name] = {}
        logger.info(f"code_location: {code_locations}")
        return code_locations

    # Started by AICoder, pid:76edab6551409d114ce70bbbd0ec2822403482b2
    def retrieve_path_by_desc(self, repo_path, desc, top_n=20):
        repo_name = os.path.basename(os.path.normpath(repo_path))
        db = lancedb.connect(DEFAULT_VECTOR_PATH)
        table_name = f"{repo_name}_path"
        try:
            table = db.open_table(table_name)
            vector_store = LanceDBVectorStore.from_table(table)
            index = VectorStoreIndex.from_vector_store(
                vector_store=vector_store, embed_model=self.embed_model)

            vector_retriever = index.as_retriever(similarity_top_k=500)
            retrieved_nodes = vector_retriever.retrieve(desc)
            reranker = SentenceTransformerRerank(
                url=self.config.reranker_url, top_n=top_n)
            reranked_nodes = reranker.postprocess_nodes(
                retrieved_nodes,
                query_bundle=QueryBundle(desc),
            )

            return "\n".join([node.get_content() for node in reranked_nodes])

        except ValueError as e:
            logger.error(f"Table '{table_name}' does not exist. Error: {e}")
            return ""
    # Ended by AICoder, pid:76edab6551409d114ce70bbbd0ec2822403482b2
