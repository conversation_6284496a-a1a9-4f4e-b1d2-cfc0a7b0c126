{"additional_special_tokens": ["<|im_start|>", "<|im_end|>", "<|object_ref_start|>", "<|object_ref_end|>", "<|box_start|>", "<|box_end|>", "<|quad_start|>", "<|quad_end|>", "<|vision_start|>", "<|vision_end|>", "<|vision_pad|>", "<|image_pad|>", "<|video_pad|>", "<|padoftext|>", "<|Function_Calling|>", "<|CR_specal_token|>", "<|System|>", "<|endofSystem|>", "<|User|>", "<|Assistant|>", "<requirement_begin>", "<requirement_end>", "<code_begin>", "<code_end>", "<textut_begin>", "<textut_end>", "<ut_begin>", "<ut_end>", "<repo_name_begin>", "<repo_name_end>", "<APT>", "<PPT>", "<IPT>", "<ZTE>", "<NO_ZTE>", "<project_name_begin>", "<project_name_end>", "<branch_begin>", "<branch_end>", "<think>", "</think>", "<|THINK_MODE|>", "<|NORMAL_MODE|>", "<|JiNOS|>", "<|JiNOS_CR|>", "<|Paletx|>"], "bos_token": {"content": "<|im_start|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, "eos_token": {"content": "<|im_end|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}, "pad_token": {"content": "<|endoftext|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false}}