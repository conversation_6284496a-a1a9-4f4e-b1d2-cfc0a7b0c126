#!/bin/bash

# 向量模型服务启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 默认配置
DEFAULT_HOST="0.0.0.0"
DEFAULT_PORT="36001"
DEFAULT_MODEL="BAAI/bge-large-zh"
DEFAULT_CONFIG="config.json"

# 解析命令行参数
HOST=${DEFAULT_HOST}
PORT=${DEFAULT_PORT}
MODEL=${DEFAULT_MODEL}
CONFIG=${DEFAULT_CONFIG}
DAEMON=false
STOP=false
STATUS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --host)
            HOST="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --model)
            MODEL="$2"
            shift 2
            ;;
        --config)
            CONFIG="$2"
            shift 2
            ;;
        --daemon|-d)
            DAEMON=true
            shift
            ;;
        --stop)
            STOP=true
            shift
            ;;
        --status)
            STATUS=true
            shift
            ;;
        --help|-h)
            echo "向量模型服务启动脚本"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --host HOST          服务监听地址 (默认: ${DEFAULT_HOST})"
            echo "  --port PORT          服务监听端口 (默认: ${DEFAULT_PORT})"
            echo "  --model MODEL        模型路径 (默认: ${DEFAULT_MODEL})"
            echo "  --config CONFIG      配置文件路径 (默认: ${DEFAULT_CONFIG})"
            echo "  --daemon, -d         后台运行"
            echo "  --stop               停止服务"
            echo "  --status             查看服务状态"
            echo "  --help, -h           显示帮助信息"
            echo ""
            echo "示例:"
            echo "  $0                                    # 前台启动服务"
            echo "  $0 --daemon                          # 后台启动服务"
            echo "  $0 --port 8080 --model custom-model  # 自定义端口和模型"
            echo "  $0 --stop                            # 停止服务"
            echo "  $0 --status                          # 查看状态"
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# PID文件路径
PID_FILE="/tmp/embedding_server_${PORT}.pid"
LOG_FILE="/tmp/embedding_server_${PORT}.log"

# 检查服务状态
check_service_status() {
    if [[ -f "${PID_FILE}" ]]; then
        local pid=$(cat "${PID_FILE}")
        if ps -p "${pid}" > /dev/null 2>&1; then
            return 0  # 服务正在运行
        else
            rm -f "${PID_FILE}"  # 清理无效的PID文件
            return 1  # 服务未运行
        fi
    else
        return 1  # 服务未运行
    fi
}

# 停止服务
stop_service() {
    log_info "正在停止向量模型服务..."
    
    if check_service_status; then
        local pid=$(cat "${PID_FILE}")
        log_info "发送停止信号到进程 ${pid}"
        
        # 优雅停止
        kill -TERM "${pid}" 2>/dev/null || true
        
        # 等待进程结束
        local count=0
        while ps -p "${pid}" > /dev/null 2>&1 && [[ ${count} -lt 30 ]]; do
            sleep 1
            ((count++))
        done
        
        # 如果进程仍在运行，强制杀死
        if ps -p "${pid}" > /dev/null 2>&1; then
            log_warn "进程未响应TERM信号，强制终止"
            kill -KILL "${pid}" 2>/dev/null || true
        fi
        
        rm -f "${PID_FILE}"
        log_info "服务已停止"
    else
        log_warn "服务未在运行"
    fi
}

# 显示服务状态
show_service_status() {
    if check_service_status; then
        local pid=$(cat "${PID_FILE}")
        log_info "服务正在运行 (PID: ${pid})"
        
        # 检查服务健康状态
        if command -v curl > /dev/null 2>&1; then
            log_info "检查服务健康状态..."
            if curl -s "http://${HOST}:${PORT}/health" > /dev/null; then
                log_info "服务健康检查通过"
            else
                log_warn "服务健康检查失败"
            fi
        fi
        
        # 显示资源使用情况
        if command -v ps > /dev/null 2>&1; then
            log_info "资源使用情况:"
            ps -p "${pid}" -o pid,ppid,pcpu,pmem,etime,cmd --no-headers
        fi
    else
        log_warn "服务未在运行"
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查Python
    if ! command -v python3 > /dev/null 2>&1; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    # 检查必要的Python包
    local required_packages=("fastapi" "uvicorn" "sentence_transformers")
    for package in "${required_packages[@]}"; do
        if ! python3 -c "import ${package}" 2>/dev/null; then
            log_error "Python包 ${package} 未安装"
            log_info "请运行: pip install -r requirements.txt"
            exit 1
        fi
    done
    
    # 检查embedding目录
    if [[ ! -d "embedding" ]]; then
        log_error "embedding目录不存在"
        exit 1
    fi
    
    # 检查服务脚本
    if [[ ! -f "embedding/embedding_server.py" ]]; then
        log_error "embedding_server.py 不存在"
        exit 1
    fi
    
    log_info "依赖检查通过"
}

# 启动服务
start_service() {
    log_info "启动向量模型服务..."
    log_info "配置: Host=${HOST}, Port=${PORT}, Model=${MODEL}"
    
    # 检查端口是否被占用
    if command -v netstat > /dev/null 2>&1; then
        if netstat -tuln | grep ":${PORT} " > /dev/null; then
            log_error "端口 ${PORT} 已被占用"
            exit 1
        fi
    elif command -v ss > /dev/null 2>&1; then
        if ss -tuln | grep ":${PORT} " > /dev/null; then
            log_error "端口 ${PORT} 已被占用"
            exit 1
        fi
    fi
    
    # 构建启动命令
    local cmd="python3 embedding/embedding_server.py --host ${HOST} --port ${PORT} --embedding-model ${MODEL}"
    
    if [[ "${DAEMON}" == "true" ]]; then
        # 后台运行
        log_info "以守护进程模式启动服务"
        nohup ${cmd} > "${LOG_FILE}" 2>&1 &
        local pid=$!
        echo "${pid}" > "${PID_FILE}"
        
        # 等待服务启动
        sleep 3
        
        if check_service_status; then
            log_info "服务启动成功 (PID: ${pid})"
            log_info "日志文件: ${LOG_FILE}"
            log_info "PID文件: ${PID_FILE}"
            
            # 等待服务完全启动
            log_info "等待服务完全启动..."
            local count=0
            while [[ ${count} -lt 30 ]]; do
                if command -v curl > /dev/null 2>&1; then
                    if curl -s "http://${HOST}:${PORT}/health" > /dev/null 2>&1; then
                        log_info "服务已就绪，可以接受请求"
                        break
                    fi
                fi
                sleep 1
                ((count++))
            done
            
            if [[ ${count} -eq 30 ]]; then
                log_warn "服务启动超时，请检查日志文件"
            fi
        else
            log_error "服务启动失败"
            exit 1
        fi
    else
        # 前台运行
        log_info "以前台模式启动服务"
        log_info "按 Ctrl+C 停止服务"
        exec ${cmd}
    fi
}

# 主逻辑
main() {
    log_info "向量模型服务管理脚本"
    
    if [[ "${STATUS}" == "true" ]]; then
        show_service_status
        exit 0
    fi
    
    if [[ "${STOP}" == "true" ]]; then
        stop_service
        exit 0
    fi
    
    # 检查服务是否已在运行
    if check_service_status; then
        log_warn "服务已在运行"
        show_service_status
        exit 1
    fi
    
    # 检查依赖
    check_dependencies
    
    # 启动服务
    start_service
}

# 信号处理
trap 'log_info "收到中断信号，正在停止服务..."; stop_service; exit 0' INT TERM

# 执行主函数
main "$@"
