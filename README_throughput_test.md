# 向量模型吞吐量测试工具

## 概述

这个工具用于测试向量模型服务的最大承受吞吐量。根据您的要求，测试从每秒5个chunk开始，以5为步长逐渐增加，每个级别测试10分钟，直到出现超时现象为止。

## 测试规格

- **测试对象**: 单个代码片段（chunk）
- **起始吞吐量**: 每秒5个chunk
- **步长**: 每次增加5个chunk
- **最大测试范围**: 每秒50个chunk
- **测试时长**: 每个级别10分钟
- **停止条件**: 超时率超过10%
- **发送方式**: 泊松分布（时间间隔指数分布）
- **超时阈值**: 10秒

## 使用方法

### 基本用法

```bash
python embedding_stress_test.py --test-mode throughput
```

### 自定义参数

```bash
python embedding_stress_test.py \
    --test-mode throughput \
    --url http://10.55.26.91:31027 \
    --max-rps 50 \
    --step-size 5 \
    --test-duration-minutes 10 \
    --timeout-threshold 10.0 \
    --output throughput_test_results.json
```

### 参数说明

- `--test-mode throughput`: 使用吞吐量测试模式
- `--url`: 向量服务的URL地址
- `--max-rps`: 最大每秒chunk数（默认50）
- `--step-size`: 每次增加的chunk数（默认5）
- `--test-duration-minutes`: 每个级别的测试时长，分钟（默认10）
- `--timeout-threshold`: 超时阈值，秒（默认10.0）
- `--output`: 结果输出文件名

## 测试流程

1. **健康检查**: 首先检查服务是否可用
2. **逐级测试**: 从5 chunks/秒开始，每次增加5 chunks/秒
3. **泊松分布发送**: 使用指数分布的时间间隔发送请求
4. **超时监控**: 监控每个级别的超时率
5. **自动停止**: 当超时率超过10%时自动停止测试
6. **结果分析**: 分析最大可持续吞吐量和性能指标

## 测试结果

测试完成后会显示：

- **总体统计**: 总请求数、成功率、超时率、响应时间分布
- **最大可持续吞吐量**: 超时率低于10%的最高RPS
- **容量规划建议**: 
  - 建议安全RPS（最大RPS的80%）
  - 2000用户所需实例数
  - 建议队列大小配置
  - 监控和扩缩容策略

## 输出示例

```
==================================================
压力测试结果摘要
==================================================
总请求数: 15000
成功请求数: 14250
成功率: 95.00%
超时请求数: 750
超时率: 5.00%
平均响应时间: 2.345s
P95响应时间: 4.567s
P99响应时间: 8.901s

最大可持续吞吐量: 25 chunks/秒

推荐配置:
最大可持续RPS: 25 chunks/秒
建议安全RPS: 20 chunks/秒
单实例最大吞吐量: 24.50 chunks/秒
2000用户所需实例数: 2
建议最大队列大小: 50
```

## 注意事项

1. **测试时间**: 完整测试可能需要较长时间（如测试到50 RPS需要约100分钟）
2. **服务影响**: 测试会对服务产生较高负载，建议在测试环境进行
3. **网络稳定性**: 确保测试环境网络稳定，避免网络问题影响测试结果
4. **资源监控**: 建议同时监控服务端的CPU、内存、队列等指标

## 队列配置建议

根据测试结果，工具会提供队列配置建议：

- **max_queue_size**: 建议设置为最大可持续RPS的2倍
- **监控阈值**: 队列长度超过最大RPS时触发扩容
- **安全边际**: 建议使用80%的最大承载能力作为日常运行点

## 故障排除

1. **服务不可用**: 检查服务URL和网络连接
2. **测试中断**: 检查服务稳定性和资源使用情况
3. **结果异常**: 检查测试数据和网络延迟

## 依赖要求

```bash
pip install aiohttp numpy
```
